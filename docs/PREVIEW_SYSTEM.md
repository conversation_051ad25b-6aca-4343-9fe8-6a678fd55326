# 📖 Rich Text Preview System

## 🚀 Overview

The GuruDevs platform now includes a comprehensive preview system for all rich text editors, allowing content creators to see exactly how their content will appear on the frontend before publishing.

## ✨ Features

### 🎯 Universal Preview
- **Available everywhere**: Works with all rich text editors across the platform
- **Real-time updates**: Preview updates automatically as you type
- **Multiple sizes**: Small, medium, large, and fullscreen preview modes
- **Word/character counts**: Track content length and readability

### 🎨 Preview Modes
- **Small**: Quick preview for short content
- **Medium**: Standard preview for most content
- **Large**: Detailed preview for long-form content
- **Fullscreen**: Immersive preview for comprehensive review

### 📊 Content Analytics
- **Word count**: Track article length for SEO optimization
- **Character count**: Monitor content density
- **Real-time stats**: Updates as you type

### 🔧 Integration Points
- **Posts**: Preview blog post content
- **Tutorials**: Preview tutorial lessons
- **Chapters**: Preview chapter overviews
- **Content Blocks**: Preview block content
- **Hero Sections**: Preview hero text
- **Call-to-Action**: Preview CTA content
- **Archive Blocks**: Preview intro content

## 🎯 How to Use

### 1. Basic Usage
1. **Navigate** to any content editor (Posts, Tutorials, etc.)
2. **Start typing** in the rich text editor
3. **Click "👁️ Preview Content"** button
4. **View** your content as it will appear on the frontend
5. **Close** with the "✕ Close" button or press `Esc`

### 2. Preview Features
- **Copy Text**: Extract plain text content to clipboard
- **Word Count**: See real-time word and character counts
- **Responsive**: Preview adapts to different screen sizes
- **Styled**: Shows content with proper typography and formatting

### 3. Keyboard Shortcuts
- **Esc**: Close preview modal
- **Auto-update**: Preview refreshes as you type (with small delay)

## 🔧 Technical Implementation

### Components Structure
```
src/components/
├── RichTextPreview/          # Core preview component
├── AdminPreviewField/        # Basic admin integration
├── PayloadPreviewField/      # Enhanced Payload integration
└── ui/                       # UI components (Button, Card, etc.)
```

### Integration Pattern
```typescript
// In collection config
{
  name: 'contentPreview',
  type: 'ui',
  admin: {
    components: {
      Field: {
        path: '@/components/PayloadPreviewField',
        exportName: 'PayloadPreviewField',
      },
    },
    description: 'Preview how your content will appear',
  },
}
```

## 📍 Where Preview is Available

### ✅ Collections with Preview
- **Posts** (`/admin/collections/posts`)
  - Content field preview
  - Word count and analytics
  
- **Tutorials** (`/admin/collections/tutorials`)
  - Tutorial content preview
  - Student-facing view simulation
  
- **Chapters** (`/admin/collections/chapters`)
  - Chapter overview preview
  - Learning objective visualization

### ✅ Blocks with Preview
- **Content Blocks**
  - Rich text content preview
  - Column layout simulation
  
- **Hero Sections**
  - Hero text preview
  - Typography and styling preview
  
- **Call-to-Action Blocks**
  - CTA content preview
  - Action-oriented formatting
  
- **Archive Blocks**
  - Intro content preview
  - Archive page simulation

## 🎨 Customization Options

### Preview Sizes
```typescript
size?: 'small' | 'medium' | 'large' | 'fullscreen'
```

### Content Analytics
```typescript
showWordCount?: boolean    // Default: true
showCharCount?: boolean    // Default: false
```

### Custom Styling
```typescript
enableProse?: boolean      // Default: true
enableGutter?: boolean     // Default: true
className?: string         // Custom CSS classes
```

### Button Customization
```typescript
buttonText?: string        // Default: "👁️ Preview Content"
buttonPosition?: 'top' | 'bottom'  // Default: 'top'
```

## 🚀 Advanced Features

### 1. Real-time Updates
The preview system automatically detects changes in the editor and updates the preview content without requiring manual refresh.

### 2. Content Extraction
The system can extract content from various editor states:
- Lexical editor instances
- Form data
- Hidden inputs
- Direct editor state access

### 3. Fallback Handling
If content cannot be extracted, the system provides helpful fallback messages and guidance.

### 4. Performance Optimization
- Debounced updates to prevent excessive re-rendering
- Efficient DOM observation
- Memory leak prevention with proper cleanup

## 🎯 Best Practices

### For Content Creators
1. **Use preview regularly** to ensure content formatting is correct
2. **Check word counts** for SEO optimization
3. **Test different screen sizes** using browser dev tools
4. **Verify links and media** appear correctly

### For Developers
1. **Add preview to new rich text fields** using the established pattern
2. **Customize preview settings** based on content type
3. **Test preview functionality** when adding new editor features
4. **Monitor performance** with large content pieces

## 🔍 Troubleshooting

### Preview Not Updating
- **Check editor focus**: Make sure the editor is active
- **Refresh page**: Sometimes a page refresh helps
- **Check console**: Look for JavaScript errors

### Content Not Showing
- **Verify field name**: Ensure the preview is watching the correct field
- **Check editor state**: Make sure content is being saved to the editor
- **Try typing**: Sometimes initial content load takes a moment

### Performance Issues
- **Large content**: Use smaller preview sizes for very long content
- **Multiple editors**: Close unused previews to improve performance
- **Browser memory**: Refresh page if preview becomes sluggish

## 🎉 Benefits

### For Content Creators
- **Confidence**: See exactly how content will appear
- **Efficiency**: Catch formatting issues before publishing
- **Analytics**: Track content metrics in real-time
- **Quality**: Ensure consistent formatting and styling

### For Developers
- **Consistency**: Standardized preview across all editors
- **Maintainability**: Reusable components and patterns
- **Extensibility**: Easy to add preview to new content types
- **User Experience**: Enhanced admin interface

## 🔮 Future Enhancements

### Planned Features
- **Mobile preview**: Simulate mobile device rendering
- **SEO preview**: Show how content appears in search results
- **Social media preview**: Preview social sharing cards
- **Print preview**: Show how content appears when printed
- **Accessibility preview**: Highlight accessibility features

### Integration Opportunities
- **Live preview**: Real-time frontend preview
- **Version comparison**: Compare different content versions
- **Collaborative editing**: Multi-user preview sessions
- **Export options**: Export preview as PDF or image
