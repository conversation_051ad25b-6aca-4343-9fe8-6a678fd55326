#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Files that need fixing based on the build output
const filesToFix = [
  'src/app/(frontend)/courses/[slug]/page.tsx',
  'src/app/(frontend)/exercises/page.tsx',
  'src/app/(frontend)/quizzes/page.tsx',
  'src/app/(frontend)/references/page.tsx',
  'src/app/(frontend)/terms/page.tsx',
  'src/app/(frontend)/tutorials/page.tsx',
  'src/components/ChapterLayout/index.tsx',
  'src/components/ExerciseLayout/index.tsx'
];

// Function to fix common issues
function fixFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`File not found: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix unescaped apostrophes in text content
  const apostropheRegex = /(\w)'(\w)/g;
  if (content.match(apostropheRegex)) {
    content = content.replace(apostropheRegex, '$1&apos;$2');
    modified = true;
  }

  // Fix unescaped quotes in text content
  const quoteRegex = /"([^"]*?)"/g;
  const matches = content.match(quoteRegex);
  if (matches) {
    matches.forEach(match => {
      // Only replace if it's in JSX text content, not in attributes
      if (content.includes(`>${match}<`) || content.includes(`> ${match} <`)) {
        const escaped = match.replace(/"/g, '&quot;');
        content = content.replace(match, escaped);
        modified = true;
      }
    });
  }

  // Fix specific patterns we know are problematic
  const fixes = [
    { from: "Let's", to: "Let&apos;s" },
    { from: "don't", to: "don&apos;t" },
    { from: "we're", to: "we&apos;re" },
    { from: "you're", to: "you&apos;re" },
    { from: "it's", to: "it&apos;s" },
    { from: "that's", to: "that&apos;s" },
    { from: "what's", to: "what&apos;s" },
    { from: "here's", to: "here&apos;s" },
    { from: "there's", to: "there&apos;s" },
    { from: "can't", to: "can&apos;t" },
    { from: "won't", to: "won&apos;t" },
    { from: "isn't", to: "isn&apos;t" },
    { from: "aren't", to: "aren&apos;t" },
    { from: "doesn't", to: "doesn&apos;t" },
    { from: "haven't", to: "haven&apos;t" },
    { from: "hasn't", to: "hasn&apos;t" },
    { from: "wouldn't", to: "wouldn&apos;t" },
    { from: "shouldn't", to: "shouldn&apos;t" },
    { from: "couldn't", to: "couldn&apos;t" }
  ];

  fixes.forEach(fix => {
    if (content.includes(fix.from)) {
      content = content.replace(new RegExp(fix.from, 'g'), fix.to);
      modified = true;
    }
  });

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Fixed: ${filePath}`);
  } else {
    console.log(`No changes needed: ${filePath}`);
  }
}

// Fix all files
console.log('Fixing lint errors...');
filesToFix.forEach(fixFile);
console.log('Done!');
