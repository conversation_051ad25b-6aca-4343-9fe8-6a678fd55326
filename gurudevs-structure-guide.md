
# Gurudevs.com Website Structure Guide

## Purpose
Gurudevs.com is a web-based educational platform for learning programming languages (HTML, CSS, JavaScript, Python, etc.) without requiring user login. The focus is on tutorials, reference documentation, quizzes, and SEO-friendly blog content.

---

## Global Layout

- **Tech Stack**: Next.js (App Router) + Tailwind CSS + TypeScript + Payload CMS
- **Routing**: SEO-friendly, clean URLs (e.g., /html, /css/flexbox, /quiz/html)
- **CMS**: Payload CMS used to manage Tutorials, References, Quizzes, Blog Posts, and Static Pages.

---

## Pages

### 1. Homepage (`/`)
- Hero section (tagline, CTA)
- Featured Tutorials (HTML, CSS, JS, Python)
- Category Cards: [Frontend, Backend, Scripting, Tools]
- “Try It Yourself” explanation section
- Blog Preview
- Footer with links

---

### 2. Tutorials (`/html/`, `/css/`, `/javascript/`, etc.)
- Sidebar: Auto-generated chapter list
- Main Area:
  - Lesson Title
  - Text Content
  - Code Block
  - “Try it Yourself” button with inline/live editor
  - Prev/Next Navigation

---

### 3. References (`/html/tags/`, `/css/properties/`)
- Table or List view:
  - Keyword | Description | Code | Output
  - Fast lookup for syntax
- Linked from tutorials

---

### 4. Quiz Pages (`/quiz/html`, `/quiz/javascript`)
- Multiple-choice quizzes
- Show result and correct answers
- Stored in CMS as collections (question, options, answer, explanation)

---

### 5. Exercises (`/exercise/html`)
- Fill in the blanks, rearrange code, or correct output
- Linked to each tutorial topic
- Optional success message or score summary

---

### 6. Blog (`/blog/`)
- Blog listing (paginated)
- Each post:
  - Markdown-based
  - SEO title, meta description
  - Linked to relevant tutorials

---

### 7. Static Pages
- `/about`: Mission, vision, about the project
- `/contact`: Form, support email
- `/privacy-policy`: Data handling, cookies
- `/terms`: Usage terms
- `/disclaimer`: Content accuracy notice
- `/sitemap.xml` & `/robots.txt`: For SEO

---

## CMS Collections (Payload CMS)

- `Tutorials`: title, slug, category, body, code examples
- `References`: term, definition, category, code, output
- `Quizzes`: question, options, correct answer, explanation
- `Blog Posts`: title, slug, body, excerpt, tags
- `StaticPages`: slug, title, content

---

## Notes for AI

- Use sidebar titles to organize TOC
- Tutorials are split into logical chapters
- Every content page must have `title`, `slug`, `seoMeta`, and `category`
- Internal linking between blog/tutorials/refs is encouraged
