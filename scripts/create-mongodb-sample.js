import { getPayload } from 'payload'
import configPromise from '../src/payload.config.ts'

async function createMongoDBSample() {
  const payload = await getPayload({ config: configPromise })

  try {
    console.log('Creating MongoDB sample data...')

    // 1. Create MongoDB category
    const mongoCategory = await payload.create({
      collection: 'categories',
      data: {
        title: 'MongoDB',
        slug: 'mongodb',
        description: 'Learn MongoDB, the popular NoSQL database',
        color: '#4DB33D',
        _status: 'published',
      },
    })
    console.log('✅ Created MongoDB category')

    // 2. Create Chapter 1: MongoDB Fundamentals
    const chapter1 = await payload.create({
      collection: 'chapters',
      data: {
        title: 'MongoDB Fundamentals',
        description: 'Learn the basics of MongoDB database',
        category: mongoCategory.id,
        difficulty: 'beginner',
        order: 1,
        estimatedTime: '3 hours',
        icon: 'database',
        overview: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    text: 'This chapter covers the fundamental concepts of MongoDB, including what it is, its features, and how it compares to traditional SQL databases.',
                  },
                ],
              },
            ],
          },
        },
        learningObjectives: [
          { objective: 'Understand what MongoDB is and its key features' },
          { objective: 'Learn the differences between MongoDB and SQL databases' },
          { objective: 'Install and set up MongoDB on your system' },
        ],
        _status: 'published',
      },
    })
    console.log('✅ Created Chapter 1: MongoDB Fundamentals')

    // 3. Create Chapter 2: Getting Started with MongoDB
    const chapter2 = await payload.create({
      collection: 'chapters',
      data: {
        title: 'Getting Started with MongoDB',
        description: 'Learn core concepts and basic operations',
        category: mongoCategory.id,
        difficulty: 'beginner',
        order: 2,
        estimatedTime: '4 hours',
        icon: 'play-circle',
        overview: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    text: 'This chapter introduces you to MongoDB core concepts like databases, collections, and documents, and teaches you basic CRUD operations.',
                  },
                ],
              },
            ],
          },
        },
        learningObjectives: [
          { objective: 'Understand databases, collections, and documents' },
          { objective: 'Learn BSON and supported data types' },
          { objective: 'Perform basic CRUD operations' },
        ],
        previousChapter: chapter1.id,
        _status: 'published',
      },
    })
    console.log('✅ Created Chapter 2: Getting Started with MongoDB')

    // Update chapter 1 to link to chapter 2
    await payload.update({
      collection: 'chapters',
      id: chapter1.id,
      data: {
        nextChapter: chapter2.id,
      },
    })

    // 4. Create Chapter 3: Querying Documents
    const chapter3 = await payload.create({
      collection: 'chapters',
      data: {
        title: 'Querying Documents in MongoDB',
        description: 'Master MongoDB query operations',
        category: mongoCategory.id,
        difficulty: 'intermediate',
        order: 3,
        estimatedTime: '5 hours',
        icon: 'search',
        overview: {
          root: {
            type: 'root',
            children: [
              {
                type: 'paragraph',
                children: [
                  {
                    text: 'Learn advanced querying techniques including query operators, working with arrays and embedded documents, and optimizing query performance.',
                  },
                ],
              },
            ],
          },
        },
        learningObjectives: [
          { objective: 'Master query operators like $eq, $gt, $in, $regex' },
          { objective: 'Work with arrays and embedded documents' },
          { objective: 'Implement sorting, projection, and pagination' },
          { objective: 'Optimize query performance' },
        ],
        previousChapter: chapter2.id,
        _status: 'published',
      },
    })
    console.log('✅ Created Chapter 3: Querying Documents')

    // Update chapter 2 to link to chapter 3
    await payload.update({
      collection: 'chapters',
      id: chapter2.id,
      data: {
        nextChapter: chapter3.id,
      },
    })

    // 5. Create tutorials for Chapter 1
    const tutorials = [
      {
        title: 'What is MongoDB? Features and Use Cases',
        excerpt: 'Learn what MongoDB is, its key features, and when to use it',
        chapter: chapter1.id,
        lessonNumber: 1,
        content: {
          root: {
            type: 'root',
            children: [
              {
                type: 'heading',
                children: [{ text: 'What is MongoDB?' }],
                tag: 'h2',
              },
              {
                type: 'paragraph',
                children: [
                  {
                    text: 'MongoDB is a widely used relational database management system (RDBMS).',
                  },
                ],
              },
              {
                type: 'paragraph',
                children: [{ text: 'MongoDB is free and open-source.' }],
              },
              {
                type: 'paragraph',
                children: [{ text: 'MongoDB is ideal for both small and large applications.' }],
              },
              {
                type: 'heading',
                children: [{ text: 'Key Features' }],
                tag: 'h2',
              },
              {
                type: 'list',
                children: [
                  {
                    type: 'listItem',
                    children: [
                      {
                        type: 'paragraph',
                        children: [{ text: 'Document-oriented NoSQL database' }],
                      },
                    ],
                  },
                  {
                    type: 'listItem',
                    children: [
                      {
                        type: 'paragraph',
                        children: [{ text: 'Flexible schema design' }],
                      },
                    ],
                  },
                  {
                    type: 'listItem',
                    children: [
                      {
                        type: 'paragraph',
                        children: [{ text: 'High performance and scalability' }],
                      },
                    ],
                  },
                ],
                tag: 'ul',
              },
            ],
          },
        },
      },
      {
        title: 'MongoDB vs SQL Databases (Key Differences)',
        excerpt: 'Understand the key differences between MongoDB and traditional SQL databases',
        chapter: chapter1.id,
        lessonNumber: 2,
        content: {
          root: {
            type: 'root',
            children: [
              {
                type: 'heading',
                children: [{ text: 'MongoDB vs SQL Databases' }],
                tag: 'h2',
              },
              {
                type: 'paragraph',
                children: [
                  {
                    text: 'MongoDB and SQL databases serve different purposes and have distinct characteristics.',
                  },
                ],
              },
              {
                type: 'heading',
                children: [{ text: 'Key Differences' }],
                tag: 'h3',
              },
              {
                type: 'list',
                children: [
                  {
                    type: 'listItem',
                    children: [
                      {
                        type: 'paragraph',
                        children: [{ text: 'Data Model: MongoDB uses documents, SQL uses tables' }],
                      },
                    ],
                  },
                  {
                    type: 'listItem',
                    children: [
                      {
                        type: 'paragraph',
                        children: [
                          { text: 'Schema: MongoDB is schema-flexible, SQL is schema-rigid' },
                        ],
                      },
                    ],
                  },
                  {
                    type: 'listItem',
                    children: [
                      {
                        type: 'paragraph',
                        children: [{ text: 'Query Language: MongoDB uses MQL, SQL uses SQL' }],
                      },
                    ],
                  },
                ],
                tag: 'ul',
              },
            ],
          },
        },
      },
      {
        title: 'Installing MongoDB (Windows, macOS, Linux)',
        excerpt: 'Step-by-step guide to install MongoDB on different operating systems',
        chapter: chapter1.id,
        lessonNumber: 3,
        content: {
          root: {
            type: 'root',
            children: [
              {
                type: 'heading',
                children: [{ text: 'Installing MongoDB' }],
                tag: 'h2',
              },
              {
                type: 'paragraph',
                children: [
                  {
                    text: 'This guide will walk you through installing MongoDB on Windows, macOS, and Linux.',
                  },
                ],
              },
              {
                type: 'heading',
                children: [{ text: 'Windows Installation' }],
                tag: 'h3',
              },
              {
                type: 'list',
                children: [
                  {
                    type: 'listItem',
                    children: [
                      {
                        type: 'paragraph',
                        children: [{ text: 'Download MongoDB Community Server' }],
                      },
                    ],
                  },
                  {
                    type: 'listItem',
                    children: [
                      {
                        type: 'paragraph',
                        children: [{ text: 'Run the installer' }],
                      },
                    ],
                  },
                  {
                    type: 'listItem',
                    children: [
                      {
                        type: 'paragraph',
                        children: [{ text: 'Configure MongoDB as a service' }],
                      },
                    ],
                  },
                ],
                tag: 'ol',
              },
            ],
          },
        },
      },
    ]

    // Create tutorials for Chapter 1
    for (const tutorialData of tutorials) {
      const tutorial = await payload.create({
        collection: 'tutorials',
        data: {
          ...tutorialData,
          category: mongoCategory.id,
          difficulty: 'beginner',
          estimatedTime: '30 minutes',
          _status: 'published',
        },
      })
      console.log(`✅ Created tutorial: ${tutorial.title}`)
    }

    console.log('\n🎉 MongoDB sample data created successfully!')
    console.log(
      'You can now visit http://localhost:3000/tutorials to see the chapter-based structure',
    )
  } catch (error) {
    console.error('Error creating sample data:', error)
  } finally {
    process.exit(0)
  }
}

createMongoDBSample()
