import { getCachedGlobal } from '@/utilities/getGlobals'
import Link from 'next/link'
import React from 'react'

import type { Footer } from '@/payload-types'

import { ThemeSelector } from '@/providers/Theme/ThemeSelector'
import { CMSLink } from '@/components/Link'
import { Logo } from '@/components/Logo/Logo'
import { brandingConfig } from '@/config/branding'
import { BookOpen, Trophy, Code, Github, Twitter, Linkedin } from 'lucide-react'

export async function Footer() {
  const footerData: Footer = await getCachedGlobal('footer', 1)()

  const navItems = footerData?.navItems || []

  const footerSections = [
    {
      title: 'Learn',
      links: [
        { href: '/tutorials', label: 'Tutorials', icon: BookOpen },
        { href: '/quizzes', label: 'Quizzes', icon: Trophy },
        { href: '/references', label: 'References', icon: Code },
        { href: '/exercises', label: 'Exercises' },
      ],
    },
    {
      title: 'Languages',
      links: [
        { href: '/tutorials/html', label: 'HTML' },
        { href: '/tutorials/css', label: 'CSS' },
        { href: '/tutorials/javascript', label: 'JavaScript' },
        { href: '/tutorials/python', label: 'Python' },
      ],
    },
    {
      title: 'Company',
      links: [
        { href: '/about', label: 'About Us' },
        { href: '/contact', label: 'Contact' },
        { href: '/privacy-policy', label: 'Privacy Policy' },
        { href: '/terms', label: 'Terms of Service' },
      ],
    },
  ]

  const socialLinks = [
    { href: 'https://github.com/gurudevs', icon: Github, label: 'GitHub' },
    { href: 'https://twitter.com/gurudevs', icon: Twitter, label: 'Twitter' },
    { href: 'https://linkedin.com/company/gurudevs', icon: Linkedin, label: 'LinkedIn' },
  ]

  return (
    <footer className="mt-auto bg-gray-900 dark:bg-gray-950 text-white">
      <div className="container py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <Link className="flex items-center mb-4" href="/">
              <Logo />
            </Link>
            <p className="text-gray-300 mb-4 max-w-md">{brandingConfig.brand.description}</p>
            <p className="text-sm text-gray-400 mb-6">{brandingConfig.brand.mission}</p>

            {/* Social Links */}
            <div className="flex gap-4">
              {socialLinks.map((social) => (
                <Link
                  key={social.href}
                  href={social.href}
                  className="text-gray-400 hover:text-brand-400 transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <span className="sr-only">{social.label}</span>
                  <social.icon className="h-5 w-5" />
                </Link>
              ))}
            </div>
          </div>

          {/* Footer Sections */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <h3 className="font-semibold text-white mb-4">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-brand-400 transition-colors text-sm flex items-center gap-2"
                    >
                      {link.icon && <link.icon className="h-4 w-4" />}
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* CMS Navigation Items */}
        {navItems.length > 0 && (
          <div className="border-t border-gray-800 pt-8 mb-8">
            <nav className="flex flex-wrap gap-6">
              {navItems.map(({ link }, i) => {
                return (
                  <CMSLink
                    key={i}
                    {...link}
                    className="text-gray-300 hover:text-brand-400 transition-colors text-sm"
                  />
                )
              })}
            </nav>
          </div>
        )}

        {/* Bottom Footer */}
        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="text-sm text-gray-400">
            © {new Date().getFullYear()} {brandingConfig.brand.name}. All rights reserved.
          </div>

          <div className="flex items-center gap-4">
            <ThemeSelector />
            <div className="text-sm text-gray-400">Made with ❤️ for developers</div>
          </div>
        </div>
      </div>
    </footer>
  )
}
