'use client'

import React from 'react'

import type { Header as HeaderType } from '@/payload-types'

import { CMSLink } from '@/components/Link'
import Link from 'next/link'
import { SearchIcon, BookOpen, Trophy, Code, Palette } from 'lucide-react'
import { Button } from '@/components/ui/button'

export const HeaderNav: React.FC<{ data: HeaderType }> = ({ data }) => {
  const navItems = data?.navItems || []

  // Default navigation items if CMS items are not available
  const defaultNavItems = [
    { href: '/courses/html', label: 'HTML', icon: BookOpen },
    { href: '/courses/css', label: 'CSS', icon: Palette },
    { href: '/courses/javascript', label: 'JavaScript', icon: Code },
    { href: '/tutorials', label: 'Tutorials', icon: BookOpen },
    { href: '/quizzes', label: 'Quizzes', icon: Trophy },
    { href: '/references', label: 'References', icon: Code },
  ]

  return (
    <nav className="flex gap-2 items-center">
      {/* CMS Navigation Items */}
      {navItems.map(({ link }, i) => {
        return <CMSLink key={i} {...link} appearance="link" />
      })}

      {/* Default Navigation Items */}
      {navItems.length === 0 &&
        defaultNavItems.map((item) => (
          <Link key={item.href} href={item.href}>
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-700 dark:text-gray-300 hover:text-brand-600 hover:bg-brand-50 dark:hover:bg-brand-900/20 transition-colors"
            >
              <item.icon className="w-4 h-4 mr-2" />
              {item.label}
            </Button>
          </Link>
        ))}

      {/* Branding Showcase Link (Development) */}
      {process.env.NODE_ENV === 'development' && (
        <Link href="/branding">
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-500 dark:text-gray-400 hover:text-brand-600 hover:bg-brand-50 dark:hover:bg-brand-900/20 transition-colors"
          >
            <Palette className="w-4 h-4 mr-2" />
            Design
          </Button>
        </Link>
      )}

      {/* Search */}
      <Link href="/search">
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-700 dark:text-gray-300 hover:text-brand-600 hover:bg-brand-50 dark:hover:bg-brand-900/20 transition-colors"
        >
          <span className="sr-only">Search</span>
          <SearchIcon className="w-4 h-4" />
        </Button>
      </Link>
    </nav>
  )
}
