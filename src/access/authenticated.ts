import type { AccessArgs } from 'payload'

import type { User } from '@/payload-types'

type isAuthenticated = (args: AccessArgs<User>) => boolean

// This function checks if user is authenticated (any logged-in user)
export const authenticated: isAuthenticated = ({ req: { user } }) => {
  return Boolean(user)
}

// This function checks if user is authenticated AND has admin role
export const authenticatedAdmin: isAuthenticated = ({ req: { user } }) => {
  return Boolean(user?.role === 'admin')
}
