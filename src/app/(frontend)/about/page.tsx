import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Target, Users, BookOpen, Code, Heart, Zap, Globe, Award } from 'lucide-react'
import { brandingConfig } from '@/config/branding'

export default function AboutPage() {
  const features = [
    {
      icon: BookOpen,
      title: 'Interactive Tutorials',
      description: 'Step-by-step tutorials with hands-on examples and real-world projects.',
    },
    {
      icon: Code,
      title: 'Coding Exercises',
      description: 'Practice your skills with coding challenges and exercises.',
    },
    {
      icon: Award,
      title: 'Quizzes & Assessments',
      description: 'Test your knowledge and track your progress with interactive quizzes.',
    },
    {
      icon: Globe,
      title: 'Free & Accessible',
      description: 'All content is free and accessible to learners worldwide.',
    },
  ]

  const stats = [
    { label: 'Programming Languages', value: '10+' },
    { label: 'Interactive Tutorials', value: '100+' },
    { label: 'Coding Exercises', value: '200+' },
    { label: 'Active Learners', value: '1000+' },
  ]

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-brand-100 dark:bg-brand-900 rounded-full">
              <Heart className="h-12 w-12 text-brand-600" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            About {brandingConfig.brand.name}
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto">
            {brandingConfig.brand.description}
          </p>
        </div>

        {/* Mission Section */}
        <section className="mb-16">
          <Card className="bg-gradient-to-r from-brand-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 border-brand-200 dark:border-gray-600">
            <CardContent className="p-8">
              <div className="text-center">
                <Target className="h-12 w-12 text-brand-600 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Our Mission
                </h2>
                <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
                  {brandingConfig.brand.mission}
                </p>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Story Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Our Story
          </h2>
          <div className="prose dark:prose-invert max-w-none">
            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
              GuruDevs was born from a simple belief: programming education should be accessible,
              interactive, and enjoyable for everyone. We noticed that many learning platforms were
              either too complex for beginners or too simplistic for those wanting to advance their
              skills.
            </p>
            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
              Our team of experienced developers and educators came together to create a platform
              that bridges this gap. We focus on practical, hands-on learning that prepares you for
              real-world programming challenges.
            </p>
            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
              Today, GuruDevs serves thousands of learners worldwide, from complete beginners taking
              their first steps in programming to experienced developers looking to master new
              technologies.
            </p>
          </div>
        </section>

        {/* Features Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            What Makes Us Different
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-brand-100 dark:bg-brand-900 rounded-lg">
                      <feature.icon className="h-6 w-6 text-brand-600" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Stats Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Our Impact
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <Card key={index}>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-brand-600 mb-2">{stat.value}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Values Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Our Values
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="text-center">
                <Users className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <CardTitle>Accessibility</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 text-center">
                  Programming education should be available to everyone, regardless of background or
                  financial situation.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <Zap className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                <CardTitle>Innovation</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 text-center">
                  We continuously improve our platform with the latest educational technologies and
                  methodologies.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <Heart className="h-12 w-12 text-red-600 mx-auto mb-4" />
                <CardTitle>Community</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 text-center">
                  We believe in the power of community and collaborative learning to accelerate
                  growth.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Team Section */}
        <section className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Our Commitment
          </h2>
          <Card className="bg-gray-50 dark:bg-gray-800">
            <CardContent className="p-8">
              <div className="text-center">
                <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
                  We&apos;re committed to providing high-quality, up-to-date programming education
                  that prepares you for success in the tech industry. Our content is regularly
                  updated to reflect the latest industry standards and best practices.
                </p>
                <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
                  Whether you&apos;re starting your programming journey or advancing your career,
                  we&apos;re here to support you every step of the way.
                </p>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* CTA Section */}
        <section className="text-center">
          <Card className="bg-gradient-to-r from-brand-600 to-blue-600 text-white">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold mb-4">Ready to Start Your Programming Journey?</h2>
              <p className="text-lg mb-6 opacity-90">
                Join thousands of learners who are already mastering programming with GuruDevs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" variant="secondary">
                  <BookOpen className="h-5 w-5 mr-2" />
                  Start Learning
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-brand-600"
                  asChild
                >
                  <Link href="/contact">
                    <Users className="h-5 w-5 mr-2" />
                    Contact Us
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}

export const metadata: Metadata = {
  title: 'About Us | GuruDevs',
  description:
    'Learn about GuruDevs mission to make programming education accessible, interactive, and enjoyable for learners worldwide.',
  openGraph: {
    title: 'About GuruDevs',
    description:
      'Learn about GuruDevs mission to make programming education accessible, interactive, and enjoyable for learners worldwide.',
    type: 'website',
  },
}
