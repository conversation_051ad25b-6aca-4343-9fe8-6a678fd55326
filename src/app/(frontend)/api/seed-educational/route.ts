import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { seedEducationalContent } from '@/endpoints/seed/educational-content'

export async function GET() {
  try {
    const payload = await getPayload({ config: configPromise })

    // Check if content already exists
    const existingCategories = await payload.find({
      collection: 'categories',
      limit: 1,
    })

    if (existingCategories.totalDocs > 0) {
      return Response.json({
        message: 'Educational content already exists. Skipping seed.',
        success: true,
      })
    }

    await seedEducationalContent(payload)

    return Response.json({
      message: 'Educational content seeded successfully!',
      success: true,
    })
  } catch (error) {
    console.error('Error seeding educational content:', error)
    return Response.json(
      {
        message: 'Error seeding educational content',
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false,
      },
      { status: 500 },
    )
  }
}
