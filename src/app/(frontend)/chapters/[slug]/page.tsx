import type { Metadata } from 'next'
import { PayloadRedirects } from '@/components/PayloadRedirects'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import { draftMode } from 'next/headers'
import React, { cache } from 'react'
// import type { Chapter } from '@/payload-types'
// // // // // // import { ChapterLayout } from '@/components/ChapterLayout'

interface Args {
  params: Promise<{
    slug?: string
  }>
}

const queryChapterBySlug = cache(async ({ slug }: { slug: string }) => {
  const { isEnabled: draft } = await draftMode()

  const payload = await getPayload({ config: configPromise })

  try {
    const chapters = await payload.find({
      collection: 'chapters',
      where: {
        slug: {
          equals: slug,
        },
      },
      limit: 1,
      draft,
    })

    return chapters.docs?.[0] || null
  } catch (error) {
    console.error('Error fetching chapter:', error)
    return null
  }
})

export default async function ChapterPage({ params }: Args) {
  const { slug = '' } = await params
  const chapter = await queryChapterBySlug({ slug })

  if (!chapter) {
    return <PayloadRedirects url={`/chapters/${slug}`} />
  }

  // For now, create a simple chapter display until we update ChapterLayout
  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">{chapter.title}</h1>
        {chapter.description && (
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">{chapter.description}</p>
        )}
        <div className="prose dark:prose-invert max-w-none">
          <p>
            Chapter content will be displayed here. The ChapterLayout component needs to be updated
            to work with the new chapter structure.
          </p>
        </div>
      </div>
    </div>
  )
}

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })
  const chapters = await payload.find({
    collection: 'chapters',
    draft: false,
    limit: 1000,
    overrideAccess: false,
    select: {
      slug: true,
    },
  })

  const params = chapters.docs.map(({ slug }) => {
    return { slug }
  })

  return params
}

export async function generateMetadata({ params }: Args): Promise<Metadata> {
  const { slug = '' } = await params
  const chapter = await queryChapterBySlug({ slug })

  const ogImage =
    typeof chapter?.meta?.image === 'object' &&
    chapter?.meta?.image !== null &&
    'url' in chapter?.meta?.image &&
    `${process.env.NEXT_PUBLIC_SERVER_URL}${chapter.meta.image.url}`

  return {
    title: chapter?.meta?.title || chapter?.title || 'Chapter',
    description: chapter?.meta?.description || chapter?.description || '',
    openGraph: {
      images: ogImage
        ? [
            {
              url: ogImage,
            },
          ]
        : undefined,
    },
  }
}
