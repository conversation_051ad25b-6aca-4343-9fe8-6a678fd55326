import type { <PERSON><PERSON><PERSON> } from 'next'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { BookOpen, Clock, Users, ArrowRight, Target, Layers } from 'lucide-react'
// import type { Chapter } from '@/payload-types'

export default async function ChaptersPage() {
  const payload = await getPayload({ config: configPromise })

  const chapters = await payload.find({
    collection: 'chapters',
    depth: 2,
    limit: 50,
    where: {
      _status: {
        equals: 'published',
      },
    },
    sort: 'order',
  })

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-blue-100 dark:bg-blue-900 rounded-full">
              <Layers className="h-12 w-12 text-blue-600" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Learning Chapters
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto">
            Structured learning paths organized into comprehensive chapters. Follow along
            step-by-step to master programming concepts from beginner to advanced levels.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardContent className="p-6 text-center">
              <BookOpen className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {chapters.docs.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Chapters</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Target className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">Coming Soon</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Lessons</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">All Levels</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Skill Levels</div>
            </CardContent>
          </Card>
        </div>

        {/* Chapters Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {chapters.docs.map((chapter: any) => (
            <Card key={chapter.id} className="group hover:shadow-lg transition-shadow duration-300">
              <CardHeader>
                <div className="flex items-start justify-between mb-2">
                  <Badge variant="secondary" className="text-xs">
                    Chapter {chapter.order || 1}
                  </Badge>
                  {chapter.difficulty && (
                    <Badge
                      variant={
                        chapter.difficulty === 'beginner'
                          ? 'default'
                          : chapter.difficulty === 'intermediate'
                            ? 'secondary'
                            : 'destructive'
                      }
                      className="text-xs"
                    >
                      {chapter.difficulty}
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-xl group-hover:text-brand-600 transition-colors">
                  {chapter.title}
                </CardTitle>
                {chapter.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
                    {chapter.description}
                  </p>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Chapter Stats */}
                  <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <BookOpen className="h-4 w-4" />
                      <span>Multiple lessons</span>
                    </div>
                    {chapter.estimatedTime && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{chapter.estimatedTime}</span>
                      </div>
                    )}
                  </div>

                  {/* Keywords */}
                  {chapter.keywords && chapter.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {chapter.keywords.slice(0, 3).map((keyword: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {keyword}
                        </Badge>
                      ))}
                      {chapter.keywords.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{chapter.keywords.length - 3} more
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Action Button */}
                  <Button asChild className="w-full group-hover:bg-brand-700 transition-colors">
                    <Link href={`/chapters/${chapter.slug}`}>
                      Start Chapter
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {chapters.docs.length === 0 && (
          <div className="text-center py-12">
            <Layers className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              No Chapters Available
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Chapters are being prepared. Check back soon for structured learning content!
            </p>
            <Button asChild>
              <Link href="/tutorials">Browse Individual Tutorials</Link>
            </Button>
          </div>
        )}

        {/* CTA Section */}
        <section className="mt-16 text-center">
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 border-blue-200 dark:border-gray-600">
            <CardContent className="p-8">
              <BookOpen className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Ready to Start Learning?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                Choose a chapter that matches your skill level and start your programming journey
                today. Each chapter builds upon the previous one for a complete learning experience.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <Link href="/tutorials">
                    <BookOpen className="h-5 w-5 mr-2" />
                    Browse All Tutorials
                  </Link>
                </Button>
                <Button size="lg" variant="outline" asChild>
                  <Link href="/about">
                    <Target className="h-5 w-5 mr-2" />
                    Learn More
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}

export const metadata: Metadata = {
  title: 'Learning Chapters | GuruDevs',
  description:
    'Explore structured programming chapters designed to take you from beginner to advanced. Follow step-by-step lessons organized by topic and difficulty.',
  openGraph: {
    title: 'Learning Chapters | GuruDevs',
    description: 'Structured programming chapters with step-by-step lessons for all skill levels.',
    type: 'website',
  },
}
