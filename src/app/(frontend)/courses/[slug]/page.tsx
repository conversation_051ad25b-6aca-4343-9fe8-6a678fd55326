import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import Link from 'next/link'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { notFound } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { BookOpen, Clock, Target, Users, Play, CheckCircle, ArrowRight } from 'lucide-react'
import { getDifficultyColor } from '@/config/branding'

interface CategoryPageProps {
  params: Promise<{
    slug: string
  }>
}

interface Chapter {
  id: string
  title: string
  description?: string
  slug: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  order: number
  estimatedTime?: string
  icon?: string
  tutorialCount: number
  isCompleted?: boolean
}

interface Category {
  id: string
  title: string
  description?: string
  color?: string
  chapters: Chapter[]
  totalTutorials: number
  totalTime: string
}

const difficultyColors = {
  beginner: 'bg-difficulty-beginner/10 text-difficulty-beginner border-difficulty-beginner/20',
  intermediate:
    'bg-difficulty-intermediate/10 text-difficulty-intermediate border-difficulty-intermediate/20',
  advanced: 'bg-difficulty-advanced/10 text-difficulty-advanced border-difficulty-advanced/20',
}

async function getCategoryWithChapters(categorySlug: string) {
  const payload = await getPayload({ config: configPromise })

  try {
    // Get category
    const categories = await payload.find({
      collection: 'categories',
      where: {
        slug: {
          equals: categorySlug,
        },
      },
      limit: 1,
    })

    if (categories.docs.length === 0) {
      return null
    }

    const category = categories.docs[0]

    if (!category) {
      return null
    }

    // Get chapters for this category
    const chapters = await payload.find({
      collection: 'chapters',
      where: {
        category: {
          equals: category.id,
        },
        _status: {
          equals: 'published',
        },
      },
      sort: 'order',
      limit: 100,
    })

    // Get tutorial count for each chapter
    const chaptersWithTutorials = await Promise.all(
      chapters.docs.map(async (chapter: any) => {
        const tutorials = await payload.find({
          collection: 'tutorials',
          where: {
            chapter: {
              equals: chapter.id,
            },
            _status: {
              equals: 'published',
            },
          },
          limit: 1000,
          pagination: false,
        })

        return {
          id: chapter.id,
          title: chapter.title,
          description: chapter.description,
          slug: chapter.slug,
          difficulty: chapter.difficulty,
          order: chapter.order,
          estimatedTime: chapter.estimatedTime,
          icon: chapter.icon,
          tutorialCount: tutorials.totalDocs,
          isCompleted: false, // This would come from user progress
        }
      }),
    )

    // Get total tutorials for category
    const allTutorials = await payload.find({
      collection: 'tutorials',
      where: {
        category: {
          equals: category.id,
        },
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
    })

    return {
      id: category.id,
      title: category.title,
      description: category.description,
      color: category.color || '#3B82F6',
      chapters: chaptersWithTutorials,
      totalTutorials: allTutorials.totalDocs,
      totalTime: '10+ hours', // Calculate based on chapter times
    }
  } catch (error) {
    console.error('Error fetching category with chapters:', error)
    return null
  }
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { slug: categorySlug } = await params
  const categoryData = await getCategoryWithChapters(categorySlug)

  if (!categoryData) {
    notFound()
  }

  const completedChapters = categoryData.chapters.filter((c) => c.isCompleted).length
  const totalChapters = categoryData.chapters.length
  const progressPercentage = totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div
              className="p-4 rounded-full"
              style={{ backgroundColor: (categoryData.color || '#3B82F6') + '20' }}
            >
              <BookOpen className="h-12 w-12" style={{ color: categoryData.color || '#3B82F6' }} />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            {categoryData.title} Course
          </h1>
          {categoryData.description && (
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
              {categoryData.description}
            </p>
          )}

          {/* Course Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <BookOpen className="h-6 w-6 text-brand-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {totalChapters}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Chapters</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Target className="h-6 w-6 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {categoryData.totalTutorials}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Lessons</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Clock className="h-6 w-6 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {categoryData.totalTime}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Duration</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Users className="h-6 w-6 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">Free</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Access</div>
            </div>
          </div>
        </div>

        {/* Progress Overview */}
        {totalChapters > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Your Progress</CardTitle>
              <CardDescription>
                Track your learning journey through the {categoryData.title} course
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {completedChapters} of {totalChapters} chapters completed
                </span>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {Math.round(progressPercentage)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div
                  className="h-3 rounded-full transition-all duration-300"
                  style={{
                    width: `${progressPercentage}%`,
                    backgroundColor: categoryData.color || '#3B82F6',
                  }}
                ></div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Chapters List */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Course Chapters</h2>
            <Badge variant="outline">{totalChapters} chapters</Badge>
          </div>

          {categoryData.chapters.length > 0 ? (
            <div className="space-y-4">
              {categoryData.chapters.map((chapter, index) => (
                <Link key={chapter.id} href={`/chapters/${chapter.slug}`}>
                  <Card className="hover:shadow-lg transition-all duration-300 cursor-pointer group">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-4">
                          <div className="flex-shrink-0">
                            {chapter.isCompleted ? (
                              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                <CheckCircle className="h-6 w-6 text-green-600" />
                              </div>
                            ) : (
                              <div
                                className="w-12 h-12 rounded-full flex items-center justify-center"
                                style={{
                                  backgroundColor: (categoryData.color || '#3B82F6') + '20',
                                }}
                              >
                                <span
                                  className="text-lg font-bold"
                                  style={{ color: categoryData.color || '#3B82F6' }}
                                >
                                  {chapter.order}
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <Badge
                                className={
                                  difficultyColors[
                                    chapter.difficulty as keyof typeof difficultyColors
                                  ] || difficultyColors.beginner
                                }
                              >
                                {chapter.difficulty}
                              </Badge>
                              <Badge variant="outline">{chapter.tutorialCount} lessons</Badge>
                              {chapter.estimatedTime && (
                                <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                  <Clock className="h-4 w-4 mr-1" />
                                  {chapter.estimatedTime}
                                </div>
                              )}
                            </div>
                            <CardTitle className="text-xl group-hover:text-brand-600 transition-colors">
                              {chapter.title}
                            </CardTitle>
                            {chapter.description && (
                              <CardDescription className="mt-2">
                                {chapter.description}
                              </CardDescription>
                            )}
                          </div>
                        </div>
                        <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-brand-600 transition-colors" />
                      </div>
                    </CardHeader>
                  </Card>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No chapters available yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                We&apos;re working on creating comprehensive chapters for {categoryData.title}.
                Check back soon!
              </p>
              <Button asChild>
                <Link href="/tutorials">Browse All Tutorials</Link>
              </Button>
            </div>
          )}
        </div>

        {/* Call to Action */}
        {categoryData.chapters.length > 0 && (
          <div className="mt-16 text-center">
            <Card
              className="border-2"
              style={{ borderColor: (categoryData.color || '#3B82F6') + '40' }}
            >
              <CardContent className="p-8">
                <div
                  className="w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4"
                  style={{ backgroundColor: (categoryData.color || '#3B82F6') + '20' }}
                >
                  <Play className="h-8 w-8" style={{ color: categoryData.color || '#3B82F6' }} />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Ready to Start Learning {categoryData.title}?
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                  Begin your journey with Chapter 1 and work your way through our structured
                  curriculum.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    size="lg"
                    style={{ backgroundColor: categoryData.color || '#3B82F6' }}
                    asChild
                  >
                    <Link href={`/chapters/${categoryData.chapters[0]?.slug}`}>
                      <Play className="h-5 w-5 mr-2" />
                      Start Chapter 1
                    </Link>
                  </Button>
                  <Button variant="outline" size="lg" asChild>
                    <Link href="/tutorials">
                      <BookOpen className="h-5 w-5 mr-2" />
                      Browse All Tutorials
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug: categorySlug } = await params
  const categoryData = await getCategoryWithChapters(categorySlug)

  if (!categoryData) {
    return {
      title: 'Course Not Found | GuruDevs',
    }
  }

  return {
    title: `${categoryData.title} Course | GuruDevs`,
    description:
      categoryData.description ||
      `Learn ${categoryData.title} programming with our comprehensive course.`,
    openGraph: {
      title: `${categoryData.title} Course`,
      description:
        categoryData.description ||
        `Learn ${categoryData.title} programming with our comprehensive course.`,
      type: 'website',
    },
  }
}
