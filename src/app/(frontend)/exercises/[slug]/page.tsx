import type { <PERSON>ada<PERSON> } from 'next'
import React from 'react'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { notFound } from 'next/navigation'
// // // // // // import { ExerciseLayout } from '@/components/ExerciseLayout'

interface ExercisePageProps {
  params: Promise<{
    slug: string
  }>
}

async function getExercise(slug: string) {
  const payload = await getPayload({ config: configPromise })

  try {
    const exercises = await payload.find({
      collection: 'exercises',
      where: {
        slug: {
          equals: slug,
        },
        _status: {
          equals: 'published',
        },
      },
      limit: 1,
      depth: 2,
    })

    if (exercises.docs.length === 0) {
      return null
    }

    return exercises.docs[0]
  } catch (error) {
    console.error('Error fetching exercise:', error)
    return null
  }
}

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })

  try {
    const exercises = await payload.find({
      collection: 'exercises',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
      },
    })

    return exercises.docs.map((exercise: any) => ({
      slug: exercise.slug,
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

export default async function ExercisePage({ params }: ExercisePageProps) {
  const { slug } = await params
  const exercise = await getExercise(slug)

  if (!exercise) {
    notFound()
  }

  // For now, create a simple exercise display until we update ExerciseLayout
  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">{exercise.title}</h1>
        {exercise.description && (
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">{exercise.description}</p>
        )}
        <div className="prose dark:prose-invert max-w-none">
          <p>
            Exercise content will be displayed here. The ExerciseLayout component needs to be
            updated to work with the new exercise structure.
          </p>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: ExercisePageProps): Promise<Metadata> {
  const { slug } = await params
  const exercise = await getExercise(slug)

  if (!exercise) {
    return {
      title: 'Exercise Not Found | GuruDevs',
    }
  }

  return {
    title: `${exercise.title} | GuruDevs`,
    description: exercise.description || `Practice coding with the ${exercise.title} exercise.`,
    openGraph: {
      title: exercise.title,
      description: exercise.description || `Practice coding with the ${exercise.title} exercise.`,
      type: 'article',
    },
  }
}
