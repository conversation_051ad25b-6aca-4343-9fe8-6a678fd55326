import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import Link from 'next/link'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Code, ArrowRight, Target, Zap, Brain } from 'lucide-react'
import { getDifficultyColor } from '@/config/branding'

interface Exercise {
  id: string
  title: string
  description: string
  slug: string
  category: {
    title: string
    slug: string
    color?: string
  }
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  type: 'code-completion' | 'fix-code' | 'build-scratch' | 'multiple-choice'
  language: string
}

interface Category {
  id: string
  title: string
  slug: string
  color?: string
  description?: string
}

const difficultyColors = {
  beginner: 'bg-difficulty-beginner/10 text-difficulty-beginner border-difficulty-beginner/20',
  intermediate:
    'bg-difficulty-intermediate/10 text-difficulty-intermediate border-difficulty-intermediate/20',
  advanced: 'bg-difficulty-advanced/10 text-difficulty-advanced border-difficulty-advanced/20',
}

const exerciseTypeColors = {
  'code-completion': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  'fix-code': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  'build-scratch': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  'multiple-choice': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
}

async function getExercises() {
  const payload = await getPayload({ config: configPromise })

  try {
    const exercises = await payload.find({
      collection: 'exercises',
      where: {
        _status: {
          equals: 'published',
        },
      },
      sort: '-publishedAt',
      limit: 50,
      depth: 2,
    })

    const categories = await payload.find({
      collection: 'categories',
      where: {
        featured: {
          equals: true,
        },
      },
      sort: 'order',
      limit: 20,
    })

    return {
      exercises: exercises.docs,
      categories: categories.docs,
    }
  } catch (error) {
    console.error('Error fetching exercises:', error)
    return {
      exercises: [],
      categories: [],
    }
  }
}

export default async function ExercisesPage() {
  const { exercises, categories } = await getExercises()

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
              <Code className="h-12 w-12 text-green-600" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Coding Exercises
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Practice your programming skills with hands-on coding exercises. From code completion to
            building from scratch, challenge yourself and improve.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardContent className="p-6 text-center">
              <Code className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {exercises.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Coding Exercises</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Target className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">4</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Exercise Types</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Brain className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {categories.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Programming Languages</div>
            </CardContent>
          </Card>
        </div>

        {/* Exercise Types */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Exercise Types</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <div className="w-12 h-12 mx-auto rounded-lg bg-blue-100 dark:bg-blue-900 flex items-center justify-center mb-3">
                  <Code className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle className="text-lg">Code Completion</CardTitle>
                <CardDescription className="text-sm">
                  Fill in missing code to complete the program
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <div className="w-12 h-12 mx-auto rounded-lg bg-red-100 dark:bg-red-900 flex items-center justify-center mb-3">
                  <Zap className="h-6 w-6 text-red-600" />
                </div>
                <CardTitle className="text-lg">Fix the Code</CardTitle>
                <CardDescription className="text-sm">
                  Debug and fix errors in existing code
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <div className="w-12 h-12 mx-auto rounded-lg bg-green-100 dark:bg-green-900 flex items-center justify-center mb-3">
                  <Target className="h-6 w-6 text-green-600" />
                </div>
                <CardTitle className="text-lg">Build from Scratch</CardTitle>
                <CardDescription className="text-sm">
                  Create complete programs from requirements
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <div className="w-12 h-12 mx-auto rounded-lg bg-purple-100 dark:bg-purple-900 flex items-center justify-center mb-3">
                  <Brain className="h-6 w-6 text-purple-600" />
                </div>
                <CardTitle className="text-lg">Multiple Choice</CardTitle>
                <CardDescription className="text-sm">
                  Choose the correct code or approach
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </section>

        {/* Categories */}
        {categories.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Practice by Language
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {categories.map((category: any) => (
                <Link key={category.id} href={`/exercises/${category.slug}`}>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                    <CardHeader className="text-center">
                      <div
                        className="w-12 h-12 mx-auto rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform"
                        style={{ backgroundColor: category.color || '#3B82F6' }}
                      >
                        <Code className="h-6 w-6 text-white" />
                      </div>
                      <CardTitle className="text-lg">{category.title}</CardTitle>
                      {category.description && (
                        <CardDescription className="text-sm">
                          {category.description}
                        </CardDescription>
                      )}
                    </CardHeader>
                  </Card>
                </Link>
              ))}
            </div>
          </section>
        )}

        {/* All Exercises */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">All Exercises</h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {exercises.length} exercises available
              </span>
            </div>
          </div>

          {exercises.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {exercises.map((exercise: any) => (
                <Link key={exercise.id} href={`/exercises/${exercise.slug}`}>
                  <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge
                          variant="secondary"
                          style={{
                            backgroundColor: exercise.category.color || '#3B82F6' + '20',
                            color: exercise.category.color || '#3B82F6',
                          }}
                        >
                          {exercise.category.title}
                        </Badge>
                        <Badge
                          className={
                            difficultyColors[
                              exercise.difficulty as keyof typeof difficultyColors
                            ] || difficultyColors.beginner
                          }
                        >
                          {exercise.difficulty}
                        </Badge>
                      </div>

                      <div className="flex items-center gap-2 mb-2">
                        <Badge
                          className={
                            exerciseTypeColors[exercise.type as keyof typeof exerciseTypeColors] ||
                            exerciseTypeColors['code-completion']
                          }
                        >
                          {exercise.type.replace('-', ' ')}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {exercise.language.toUpperCase()}
                        </Badge>
                      </div>

                      <CardTitle className="text-lg line-clamp-2">{exercise.title}</CardTitle>
                      <CardDescription className="line-clamp-3">
                        {exercise.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          Start Coding
                        </span>
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Code className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No exercises available yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                We&apos;re working on creating engaging coding exercises for you. Check back soon!
              </p>
              <Button asChild>
                <Link href="/tutorials">Browse Tutorials Instead</Link>
              </Button>
            </div>
          )}
        </section>

        {/* Call to Action */}
        <section className="mt-16 text-center">
          <Card className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 border-green-200 dark:border-gray-600">
            <CardContent className="p-8">
              <Code className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Ready to Start Coding?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                Practice makes perfect! Start with beginner exercises and work your way up to
                advanced challenges. Each exercise includes hints and solutions to help you learn.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg">
                  <Code className="h-5 w-5 mr-2" />
                  Start First Exercise
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/tutorials">
                    <Target className="h-5 w-5 mr-2" />
                    Learn First
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}

export const metadata: Metadata = {
  title: 'Coding Exercises | GuruDevs',
  description:
    'Practice your programming skills with hands-on coding exercises. From code completion to building from scratch, challenge yourself and improve.',
}
