import type { Metadata } from 'next'
import React from 'react'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { HomePage } from '@/components/HomePage'

async function getHomePageData() {
  const payload = await getPayload({ config: configPromise })

  try {
    // Get featured categories
    const categories = await payload.find({
      collection: 'categories',
      where: {
        featured: {
          equals: true,
        },
      },
      sort: 'order',
      limit: 8,
    })

    // Get featured tutorials
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        _status: {
          equals: 'published',
        },
      },
      sort: '-publishedAt',
      limit: 6,
      depth: 2,
    })

    // Get stats (you can implement actual counting later)
    const stats = {
      totalTutorials: tutorials.totalDocs || 0,
      totalUsers: 1000, // Placeholder
      totalQuizzes: 50, // Placeholder
    }

    return {
      featuredCategories: categories.docs.map((cat: any) => ({
        id: cat.id,
        title: cat.title,
        description: cat.description || `Learn ${cat.title} programming`,
        slug: cat.slug,
        color: cat.color || '#3B82F6',
        featured: cat.featured,
      })),
      featuredTutorials: tutorials.docs.map((tutorial: any) => ({
        id: tutorial.id,
        title: tutorial.title,
        excerpt: tutorial.excerpt || `Learn ${tutorial.title}`,
        slug: tutorial.slug,
        category: {
          id: tutorial.category.id,
          title: tutorial.category.title,
          description:
            tutorial.category.description || `Learn ${tutorial.category.title} programming`,
          slug: tutorial.category.slug,
          color: tutorial.category.color || '#3B82F6',
          featured: tutorial.category.featured || false,
        },
        difficulty: tutorial.difficulty || 'beginner',
        estimatedTime: tutorial.estimatedTime || '30 min',
      })),
      stats,
    }
  } catch (error) {
    console.error('Error fetching homepage data:', error)
    return {
      featuredCategories: [],
      featuredTutorials: [],
      stats: {
        totalTutorials: 0,
        totalUsers: 0,
        totalQuizzes: 0,
      },
    }
  }
}

export default async function Home() {
  const data = await getHomePageData()

  return (
    <HomePage
      featuredCategories={data.featuredCategories}
      featuredTutorials={data.featuredTutorials}
      stats={data.stats}
    />
  )
}

export const metadata: Metadata = {
  title: 'GuruDevs - Learn Programming Online',
  description:
    'Master programming languages with interactive tutorials, hands-on exercises, and comprehensive references. Start your coding journey today!',
  openGraph: {
    title: 'GuruDevs - Learn Programming Online',
    description:
      'Master programming languages with interactive tutorials, hands-on exercises, and comprehensive references. Start your coding journey today!',
    type: 'website',
  },
}
