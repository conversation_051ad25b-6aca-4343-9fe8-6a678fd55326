import type { Metadata } from 'next'
import React from 'react'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
// // // // // // import { HomePage } from '@/components/HomePage'

async function getHomePageData() {
  const payload = await getPayload({ config: configPromise })

  try {
    // Get featured categories
    const categories = await payload.find({
      collection: 'categories',
      where: {
        featured: {
          equals: true,
        },
      },
      sort: 'order',
      limit: 8,
    })

    // Get featured tutorials
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        _status: {
          equals: 'published',
        },
      },
      sort: '-publishedAt',
      limit: 6,
      depth: 2,
    })

    // Get stats (you can implement actual counting later)
    const stats = {
      totalTutorials: tutorials.totalDocs || 0,
      totalUsers: 1000, // Placeholder
      totalQuizzes: 50, // Placeholder
    }

    return {
      featuredCategories: categories.docs.map((cat: any) => ({
        id: cat.id,
        title: cat.title,
        description: cat.description || `Learn ${cat.title} programming`,
        slug: cat.slug,
        color: cat.color || '#3B82F6',
        featured: cat.featured,
      })),
      featuredTutorials: tutorials.docs.map((tutorial: any) => ({
        id: tutorial.id,
        title: tutorial.title,
        excerpt: tutorial.excerpt,
        slug: tutorial.slug,
        category: {
          title: tutorial.category.title,
          slug: tutorial.category.slug,
          color: tutorial.category.color || '#3B82F6',
        },
        difficulty: tutorial.difficulty,
        estimatedTime: tutorial.estimatedTime,
      })),
      stats,
    }
  } catch (error) {
    console.error('Error fetching homepage data:', error)
    return {
      featuredCategories: [],
      featuredTutorials: [],
      stats: {
        totalTutorials: 0,
        totalUsers: 0,
        totalQuizzes: 0,
      },
    }
  }
}

export default async function Home() {
  const data = await getHomePageData()

  // For now, create a simple homepage display until we update HomePage component
  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Learn Programming with GuruDevs
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Master programming skills with our comprehensive tutorials, interactive exercises, and
            structured learning paths.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {data.featuredTutorials.map((tutorial: any) => (
            <div key={tutorial.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-semibold mb-2">{tutorial.title}</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">{tutorial.excerpt}</p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">{tutorial.difficulty}</span>
                <span className="text-sm text-gray-500">{tutorial.estimatedTime}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export const metadata: Metadata = {
  title: 'GuruDevs - Learn Programming Online',
  description:
    'Master programming languages with interactive tutorials, hands-on exercises, and comprehensive references. Start your coding journey today!',
  openGraph: {
    title: 'GuruDevs - Learn Programming Online',
    description:
      'Master programming languages with interactive tutorials, hands-on exercises, and comprehensive references. Start your coding journey today!',
    type: 'website',
  },
}
