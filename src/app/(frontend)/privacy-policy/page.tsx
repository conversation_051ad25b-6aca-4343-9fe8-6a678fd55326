import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import <PERSON> from 'next/link'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Shield, Eye, Cookie, Database, Mail, Lock } from 'lucide-react'

export default function PrivacyPolicyPage() {
  const lastUpdated = 'December 2024'

  const sections = [
    {
      icon: Eye,
      title: 'Information We Collect',
      content: [
        'When you visit GuruDevs, we may collect certain information automatically, including your IP address, browser type, operating system, and pages visited.',
        'If you contact us through our contact form, we collect the information you provide, such as your name, email address, and message content.',
        'We use local storage to save your progress on tutorials and quizzes, but this data remains on your device and is not transmitted to our servers.',
      ],
    },
    {
      icon: Database,
      title: 'How We Use Your Information',
      content: [
        'To provide and maintain our educational services',
        'To respond to your inquiries and provide customer support',
        'To analyze website usage and improve our content and user experience',
        'To detect and prevent technical issues and security threats',
        'We do not sell, trade, or rent your personal information to third parties.',
      ],
    },
    {
      icon: <PERSON><PERSON>,
      title: 'Cookies and Tracking',
      content: [
        'We use essential cookies to ensure our website functions properly.',
        'We may use analytics cookies to understand how visitors interact with our site.',
        'We use local storage to save your learning progress and preferences.',
        'You can control cookie settings through your browser preferences.',
        'Disabling cookies may affect some functionality of our website.',
      ],
    },
    {
      icon: Lock,
      title: 'Data Security',
      content: [
        'We implement appropriate security measures to protect your information.',
        'All data transmission is encrypted using SSL/TLS protocols.',
        'We regularly update our security practices and monitor for vulnerabilities.',
        'Access to personal information is restricted to authorized personnel only.',
        'We cannot guarantee absolute security, but we strive to use industry-standard practices.',
      ],
    },
    {
      icon: Mail,
      title: 'Third-Party Services',
      content: [
        'We may use third-party services for analytics, hosting, and other operational purposes.',
        'These services may have their own privacy policies and data practices.',
        'We do not share personal information with third parties for marketing purposes.',
        'Any third-party integrations are carefully vetted for security and privacy compliance.',
      ],
    },
    {
      icon: Shield,
      title: 'Your Rights',
      content: [
        'You have the right to access, update, or delete your personal information.',
        'You can opt out of non-essential data collection at any time.',
        'You can request a copy of the data we have about you.',
        'You can request that we delete your data, subject to legal requirements.',
        'Contact <NAME_EMAIL> to exercise these rights.',
      ],
    },
  ]

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-green-100 dark:bg-green-900 rounded-full">
              <Shield className="h-12 w-12 text-green-600" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Privacy Policy
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto">
            Your privacy is important to us. This policy explains how we collect, use, and protect
            your information when you use GuruDevs.
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
            Last updated: {lastUpdated}
          </p>
        </div>

        {/* Introduction */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              At GuruDevs, we are committed to protecting your privacy and ensuring transparency
              about our data practices. This Privacy Policy describes how we collect, use, and
              safeguard your information when you visit our website and use our educational
              services.
            </p>
          </CardContent>
        </Card>

        {/* Policy Sections */}
        <div className="space-y-8">
          {sections.map((section, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                    <section.icon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                  </div>
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {section.content.map((item, itemIndex) => (
                    <li
                      key={itemIndex}
                      className="text-gray-700 dark:text-gray-300 leading-relaxed"
                    >
                      • {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Children's Privacy */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Children&apos;s Privacy</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
              GuruDevs is designed to be safe for users of all ages, including children under 13. We
              do not knowingly collect personal information from children under 13 without parental
              consent.
            </p>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              If you are a parent or guardian and believe your child has provided us with personal
              information, please contact us immediately so we can remove such information.
            </p>
          </CardContent>
        </Card>

        {/* Changes to Policy */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Changes to This Policy</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
              We may update this Privacy Policy from time to time to reflect changes in our
              practices or for other operational, legal, or regulatory reasons.
            </p>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              When we make changes, we will update the &quot;Last updated&quot; date at the top of
              this policy. We encourage you to review this policy periodically to stay informed
              about how we protect your information.
            </p>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card className="mt-8 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
          <CardHeader>
            <CardTitle className="text-blue-800 dark:text-blue-200">
              Questions About This Policy?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-blue-700 dark:text-blue-300 leading-relaxed mb-4">
              If you have any questions about this Privacy Policy or our data practices, please
              don&apos;t hesitate to contact us:
            </p>
            <div className="space-y-2 text-blue-700 dark:text-blue-300">
              <p>• Email: <EMAIL></p>
              <p>
                • Contact Form:{' '}
                <Link href="/contact" className="underline hover:no-underline">
                  gurudevs.com/contact
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Effective Date */}
        <div className="text-center mt-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            This Privacy Policy is effective as of {lastUpdated} and applies to all users of
            GuruDevs.
          </p>
        </div>
      </div>
    </div>
  )
}

export const metadata: Metadata = {
  title: 'Privacy Policy | GuruDevs',
  description:
    'Learn how GuruDevs collects, uses, and protects your information. Our commitment to your privacy and data security.',
  openGraph: {
    title: 'Privacy Policy | GuruDevs',
    description: 'Learn how GuruDevs collects, uses, and protects your information.',
    type: 'website',
  },
}
