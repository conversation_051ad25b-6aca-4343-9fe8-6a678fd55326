import React from 'react'
import { redirect } from 'next/navigation'
import { getMeUser } from '@/utilities/getMeUser'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { User, Mail, Calendar, Settings, Shield } from 'lucide-react'
import Link from 'next/link'

export default async function ProfilePage() {
  let user
  
  try {
    const userData = await getMeUser()
    user = userData.user
  } catch (error) {
    // User is not authenticated, redirect to login
    redirect('/admin/login?redirect=/profile')
  }

  if (!user) {
    redirect('/admin/login?redirect=/profile')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'user':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">My Profile</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage your account settings and preferences
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          {/* Profile Card */}
          <div className="md:col-span-1">
            <Card>
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <Avatar className="h-24 w-24">
                    <AvatarImage 
                      src={user.avatar ? `/api/media/${user.avatar}` : undefined} 
                      alt={user.name || 'User'} 
                    />
                    <AvatarFallback className="text-lg">
                      {getInitials(user.name || 'User')}
                    </AvatarFallback>
                  </Avatar>
                </div>
                <CardTitle className="text-xl">{user.name || 'User'}</CardTitle>
                <CardDescription className="flex items-center justify-center gap-2">
                  <Mail className="h-4 w-4" />
                  {user.email}
                </CardDescription>
                <div className="flex justify-center mt-2">
                  <Badge className={getRoleColor(user.role || 'user')}>
                    <Shield className="h-3 w-3 mr-1" />
                    {user.role === 'admin' ? 'Administrator' : 'User'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="text-center">
                <div className="flex items-center justify-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                  <Calendar className="h-4 w-4 mr-2" />
                  Member since {formatDate(user.createdAt)}
                </div>
                <Link href="/admin/account">
                  <Button variant="outline" className="w-full">
                    <Settings className="h-4 w-4 mr-2" />
                    Edit Profile
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          {/* Account Information */}
          <div className="md:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Account Information
                </CardTitle>
                <CardDescription>
                  Your basic account details and settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Full Name
                    </label>
                    <p className="text-gray-900 dark:text-white">{user.name || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Email Address
                    </label>
                    <p className="text-gray-900 dark:text-white">{user.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Account Type
                    </label>
                    <p className="text-gray-900 dark:text-white">
                      {user.role === 'admin' ? 'Administrator' : 'Regular User'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Last Updated
                    </label>
                    <p className="text-gray-900 dark:text-white">{formatDate(user.updatedAt)}</p>
                  </div>
                </div>
                
                {user.bio && (
                  <div>
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Bio
                    </label>
                    <p className="text-gray-900 dark:text-white mt-1">{user.bio}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Preferences */}
            {user.preferences && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Preferences
                  </CardTitle>
                  <CardDescription>
                    Your account preferences and settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Email Notifications
                      </label>
                      <p className="text-gray-900 dark:text-white">
                        {user.preferences.emailNotifications ? 'Enabled' : 'Disabled'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Theme Preference
                      </label>
                      <p className="text-gray-900 dark:text-white capitalize">
                        {user.preferences.theme || 'System'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Admin Features */}
            {user.role === 'admin' && (
              <Card className="border-red-200 dark:border-red-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
                    <Shield className="h-5 w-5" />
                    Administrator Access
                  </CardTitle>
                  <CardDescription>
                    You have administrator privileges on this platform
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Link href="/admin">
                      <Button className="w-full md:w-auto">
                        Access Admin Panel
                      </Button>
                    </Link>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      As an administrator, you can manage users, create content, and access all platform features.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <Link
            href="/"
            className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            ← Back to Home
          </Link>
        </div>
      </div>
    </div>
  )
}
