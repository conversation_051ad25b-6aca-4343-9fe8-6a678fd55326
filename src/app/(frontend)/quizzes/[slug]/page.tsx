import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { notFound } from 'next/navigation'
// // // // // // import { QuizLayout } from '@/components/QuizLayout'

interface QuizPageProps {
  params: Promise<{
    slug: string
  }>
}

async function getQuiz(slug: string) {
  const payload = await getPayload({ config: configPromise })

  try {
    const quizzes = await payload.find({
      collection: 'quizzes',
      where: {
        slug: {
          equals: slug,
        },
        _status: {
          equals: 'published',
        },
      },
      limit: 1,
      depth: 2,
    })

    if (quizzes.docs.length === 0) {
      return null
    }

    return quizzes.docs[0]
  } catch (error) {
    console.error('Error fetching quiz:', error)
    return null
  }
}

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })

  try {
    const quizzes = await payload.find({
      collection: 'quizzes',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
      },
    })

    return quizzes.docs.map((quiz: any) => ({
      slug: quiz.slug,
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

export default async function QuizPage({ params }: QuizPageProps) {
  const { slug } = await params
  const quiz = await getQuiz(slug)

  if (!quiz) {
    notFound()
  }

  // For now, create a simple quiz display until we update QuizLayout
  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">{quiz.title}</h1>
        {quiz.description && (
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">{quiz.description}</p>
        )}
        <div className="prose dark:prose-invert max-w-none">
          <p>
            Quiz content will be displayed here. The QuizLayout component needs to be updated to
            work with the new quiz structure.
          </p>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: QuizPageProps): Promise<Metadata> {
  const { slug } = await params
  const quiz = await getQuiz(slug)

  if (!quiz) {
    return {
      title: 'Quiz Not Found | GuruDevs',
    }
  }

  return {
    title: `${quiz.title} | GuruDevs`,
    description: quiz.description || `Test your knowledge with the ${quiz.title} quiz.`,
    openGraph: {
      title: quiz.title,
      description: quiz.description || `Test your knowledge with the ${quiz.title} quiz.`,
      type: 'article',
    },
  }
}
