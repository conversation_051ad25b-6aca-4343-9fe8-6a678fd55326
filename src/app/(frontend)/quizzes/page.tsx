import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import Link from 'next/link'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Trophy, Clock, ArrowRight, Target, Brain } from 'lucide-react'
import { getDifficultyColor } from '@/config/branding'

interface Quiz {
  id: string
  title: string
  description?: string
  slug: string
  category: {
    title: string
    slug: string
    color?: string
  }
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  timeLimit: number
  passingScore: number
  questions: any[]
}

interface Category {
  id: string
  title: string
  slug: string
  color?: string
  description?: string
}

const difficultyColors = {
  beginner: 'bg-difficulty-beginner/10 text-difficulty-beginner border-difficulty-beginner/20',
  intermediate:
    'bg-difficulty-intermediate/10 text-difficulty-intermediate border-difficulty-intermediate/20',
  advanced: 'bg-difficulty-advanced/10 text-difficulty-advanced border-difficulty-advanced/20',
}

async function getQuizzes() {
  const payload = await getPayload({ config: configPromise })

  try {
    const quizzes = await payload.find({
      collection: 'quizzes',
      where: {
        _status: {
          equals: 'published',
        },
      },
      sort: '-publishedAt',
      limit: 50,
      depth: 2,
    })

    const categories = await payload.find({
      collection: 'categories',
      where: {
        featured: {
          equals: true,
        },
      },
      sort: 'order',
      limit: 20,
    })

    return {
      quizzes: quizzes.docs,
      categories: categories.docs,
    }
  } catch (error) {
    console.error('Error fetching quizzes:', error)
    return {
      quizzes: [],
      categories: [],
    }
  }
}

export default async function QuizzesPage() {
  const { quizzes, categories } = await getQuizzes()

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-yellow-100 dark:bg-yellow-900 rounded-full">
              <Trophy className="h-12 w-12 text-yellow-600" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Programming Quizzes
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Test your programming knowledge with our interactive quizzes. Challenge yourself and
            track your progress across different topics.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardContent className="p-6 text-center">
              <Trophy className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {quizzes.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Available Quizzes</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Target className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">70%</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Average Passing Score</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Brain className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {categories.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Topics Covered</div>
            </CardContent>
          </Card>
        </div>

        {/* Categories */}
        {categories.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Quiz Categories
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {categories.map((category: any) => (
                <Link key={category.id} href={`/quizzes/${category.slug}`}>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                    <CardHeader className="text-center">
                      <div
                        className="w-12 h-12 mx-auto rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform"
                        style={{ backgroundColor: category.color || '#3B82F6' }}
                      >
                        <Trophy className="h-6 w-6 text-white" />
                      </div>
                      <CardTitle className="text-lg">{category.title}</CardTitle>
                      {category.description && (
                        <CardDescription className="text-sm">
                          {category.description}
                        </CardDescription>
                      )}
                    </CardHeader>
                  </Card>
                </Link>
              ))}
            </div>
          </section>
        )}

        {/* All Quizzes */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">All Quizzes</h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {quizzes.length} quizzes available
              </span>
            </div>
          </div>

          {quizzes.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {quizzes.map((quiz: any) => (
                <Link key={quiz.id} href={`/quizzes/${quiz.slug}`}>
                  <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge
                          variant="secondary"
                          style={{
                            backgroundColor: quiz.category.color || '#3B82F6' + '20',
                            color: quiz.category.color || '#3B82F6',
                          }}
                        >
                          {quiz.category.title}
                        </Badge>
                        <Badge
                          className={
                            difficultyColors[quiz.difficulty as keyof typeof difficultyColors] ||
                            difficultyColors.beginner
                          }
                        >
                          {quiz.difficulty}
                        </Badge>
                      </div>
                      <CardTitle className="text-lg line-clamp-2">{quiz.title}</CardTitle>
                      {quiz.description && (
                        <CardDescription className="line-clamp-3">
                          {quiz.description}
                        </CardDescription>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 mb-4">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Questions:</span>
                          <span className="font-medium">{quiz.questions?.length || 0}</span>
                        </div>

                        {quiz.timeLimit > 0 && (
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Time Limit:</span>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span className="font-medium">{quiz.timeLimit} min</span>
                            </div>
                          </div>
                        )}

                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600 dark:text-gray-400">Passing Score:</span>
                          <span className="font-medium">{quiz.passingScore}%</span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <Button size="sm" className="w-full">
                          Start Quiz
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Trophy className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No quizzes available yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                We&apos;re working on creating engaging quizzes for you. Check back soon!
              </p>
              <Button asChild>
                <Link href="/tutorials">Browse Tutorials Instead</Link>
              </Button>
            </div>
          )}
        </section>

        {/* Call to Action */}
        <section className="mt-16 text-center">
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 border-blue-200 dark:border-gray-600">
            <CardContent className="p-8">
              <Trophy className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Ready to Test Your Skills?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                Take our quizzes to assess your programming knowledge and identify areas for
                improvement. Each quiz provides instant feedback and explanations.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg">
                  <Trophy className="h-5 w-5 mr-2" />
                  Start Your First Quiz
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/tutorials">
                    <Target className="h-5 w-5 mr-2" />
                    Study First
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}

export const metadata: Metadata = {
  title: 'Programming Quizzes | GuruDevs',
  description:
    'Test your programming knowledge with interactive quizzes. Challenge yourself and track your progress across different programming topics.',
}
