import type { <PERSON>ada<PERSON> } from 'next'
import React from 'react'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { notFound } from 'next/navigation'
// // // // // // import { ReferenceLayout } from '@/components/ReferenceLayout'

interface ReferencePageProps {
  params: Promise<{
    slug: string
  }>
}

async function getReference(slug: string) {
  const payload = await getPayload({ config: configPromise })

  try {
    const references = await payload.find({
      collection: 'references',
      where: {
        slug: {
          equals: slug,
        },
        _status: {
          equals: 'published',
        },
      },
      limit: 1,
      depth: 2,
    })

    if (references.docs.length === 0) {
      return null
    }

    return references.docs[0]
  } catch (error) {
    console.error('Error fetching reference:', error)
    return null
  }
}

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })

  try {
    const references = await payload.find({
      collection: 'references',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
      },
    })

    return references.docs.map((reference: any) => ({
      slug: reference.slug,
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

export default async function ReferencePage({ params }: ReferencePageProps) {
  const { slug } = await params
  const reference = await getReference(slug)

  if (!reference) {
    notFound()
  }

  // For now, create a simple reference display until we update ReferenceLayout
  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">{reference.term}</h1>
        {reference.definition && (
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">{reference.definition}</p>
        )}
        <div className="prose dark:prose-invert max-w-none">
          <p>
            Reference content will be displayed here. The ReferenceLayout component needs to be
            updated to work with the new reference structure.
          </p>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: ReferencePageProps): Promise<Metadata> {
  const { slug } = await params
  const reference = await getReference(slug)

  if (!reference) {
    return {
      title: 'Reference Not Found | GuruDevs',
    }
  }

  return {
    title: `${reference.term} Reference | GuruDevs`,
    description:
      reference.definition ||
      `Learn about ${reference.term} with our comprehensive reference guide.`,
    openGraph: {
      title: `${reference.term} Reference`,
      description:
        reference.definition ||
        `Learn about ${reference.term} with our comprehensive reference guide.`,
      type: 'article',
    },
  }
}
