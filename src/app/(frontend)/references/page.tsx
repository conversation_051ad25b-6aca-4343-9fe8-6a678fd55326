import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import Link from 'next/link'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { BookOpen, Search, Code, ArrowRight } from 'lucide-react'

interface Reference {
  id: string
  term: string
  definition: string
  slug: string
  category: {
    title: string
    slug: string
    color?: string
  }
  subcategory?: string
}

interface Category {
  id: string
  title: string
  slug: string
  color?: string
  description?: string
}

async function getReferences() {
  const payload = await getPayload({ config: configPromise })

  try {
    const references = await payload.find({
      collection: 'references',
      where: {
        _status: {
          equals: 'published',
        },
      },
      sort: 'term',
      limit: 50,
      depth: 2,
    })

    const categories = await payload.find({
      collection: 'categories',
      where: {
        featured: {
          equals: true,
        },
      },
      sort: 'order',
      limit: 20,
    })

    return {
      references: references.docs,
      categories: categories.docs,
    }
  } catch (error) {
    console.error('Error fetching references:', error)
    return {
      references: [],
      categories: [],
    }
  }
}

export default async function ReferencesPage() {
  const { references, categories } = await getReferences()

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Programming References
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Quick reference guide for programming languages, functions, properties, and syntax. Find
            what you need fast.
          </p>
        </div>

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search references... (e.g., 'div', 'flexbox', 'map')"
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Categories */}
        {categories.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Browse by Category
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {categories.map((category: any) => (
                <Link key={category.id} href={`/references/${category.slug}`}>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                    <CardHeader className="text-center">
                      <div
                        className="w-12 h-12 mx-auto rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform"
                        style={{ backgroundColor: category.color || '#3B82F6' }}
                      >
                        <Code className="h-6 w-6 text-white" />
                      </div>
                      <CardTitle className="text-lg">{category.title}</CardTitle>
                      {category.description && (
                        <CardDescription className="text-sm">
                          {category.description}
                        </CardDescription>
                      )}
                    </CardHeader>
                  </Card>
                </Link>
              ))}
            </div>
          </section>
        )}

        {/* All References */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">All References</h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {references.length} references available
              </span>
            </div>
          </div>

          {references.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {references.map((reference: any) => (
                <Link key={reference.id} href={`/references/${reference.slug}`}>
                  <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge
                          variant="secondary"
                          style={{
                            backgroundColor: reference.category.color || '#3B82F6' + '20',
                            color: reference.category.color || '#3B82F6',
                          }}
                        >
                          {reference.category.title}
                        </Badge>
                        {reference.subcategory && (
                          <Badge variant="outline" className="text-xs">
                            {reference.subcategory}
                          </Badge>
                        )}
                      </div>
                      <CardTitle className="text-lg font-mono">{reference.term}</CardTitle>
                      <CardDescription className="line-clamp-3">
                        {reference.definition}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          Quick Reference
                        </span>
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No references available yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                We&apos;re working on creating comprehensive reference guides for you. Check back
                soon!
              </p>
              <Button asChild>
                <Link href="/tutorials">Browse Tutorials Instead</Link>
              </Button>
            </div>
          )}
        </section>
      </div>
    </div>
  )
}

export const metadata: Metadata = {
  title: 'Programming References | GuruDevs',
  description:
    'Quick reference guide for programming languages, functions, properties, and syntax. Find what you need fast.',
}
