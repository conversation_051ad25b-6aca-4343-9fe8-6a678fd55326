'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search, BookOpen, Trophy, Code, ArrowRight, Filter } from 'lucide-react'

interface SearchResult {
  id: string
  title: string
  description: string
  slug: string
  type: 'tutorial' | 'reference' | 'quiz' | 'exercise'
  category: {
    title: string
    slug: string
    color?: string
  }
  difficulty?: 'beginner' | 'intermediate' | 'advanced'
}

const typeIcons = {
  tutorial: BookOpen,
  reference: Code,
  quiz: Trophy,
  exercise: Code,
}

const typeColors = {
  tutorial: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  reference: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  quiz: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  exercise: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
}

const difficultyColors = {
  beginner: 'bg-difficulty-beginner/10 text-difficulty-beginner border-difficulty-beginner/20',
  intermediate:
    'bg-difficulty-intermediate/10 text-difficulty-intermediate border-difficulty-intermediate/20',
  advanced: 'bg-difficulty-advanced/10 text-difficulty-advanced border-difficulty-advanced/20',
}

export default function SearchPage() {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all')

  // Mock search function - in a real app, this would call an API
  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([])
      return
    }

    setIsLoading(true)

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500))

    // Mock search results
    const mockResults: SearchResult[] = [
      {
        id: '1',
        title: 'HTML Introduction',
        description: 'Learn the basics of HTML and how to create your first web page',
        slug: 'html-introduction',
        type: 'tutorial',
        category: { title: 'HTML', slug: 'html', color: '#E34F26' },
        difficulty: 'beginner',
      },
      {
        id: '2',
        title: 'CSS Flexbox',
        description: 'Master CSS Flexbox for responsive layouts',
        slug: 'css-flexbox',
        type: 'tutorial',
        category: { title: 'CSS', slug: 'css', color: '#1572B6' },
        difficulty: 'intermediate',
      },
      {
        id: '3',
        title: 'JavaScript Variables',
        description: 'Understanding variables, let, const, and var in JavaScript',
        slug: 'javascript-variables',
        type: 'reference',
        category: { title: 'JavaScript', slug: 'javascript', color: '#F7DF1E' },
      },
      {
        id: '4',
        title: 'HTML Quiz',
        description: 'Test your HTML knowledge with this comprehensive quiz',
        slug: 'html-quiz',
        type: 'quiz',
        category: { title: 'HTML', slug: 'html', color: '#E34F26' },
        difficulty: 'beginner',
      },
      {
        id: '5',
        title: 'Build a Navigation Bar',
        description: 'Create a responsive navigation bar using HTML and CSS',
        slug: 'build-navigation-bar',
        type: 'exercise',
        category: { title: 'CSS', slug: 'css', color: '#1572B6' },
        difficulty: 'intermediate',
      },
    ]

    // Filter results based on query
    const filteredResults = mockResults.filter(
      (result) =>
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.category.title.toLowerCase().includes(searchQuery.toLowerCase()),
    )

    setResults(filteredResults)
    setIsLoading(false)
  }

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(query)
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [query])

  const filteredResults = results.filter((result) => {
    const typeMatch = selectedType === 'all' || result.type === selectedType
    const difficultyMatch = selectedDifficulty === 'all' || result.difficulty === selectedDifficulty
    return typeMatch && difficultyMatch
  })

  const getResultUrl = (result: SearchResult) => {
    switch (result.type) {
      case 'tutorial':
        return `/tutorials/${result.slug}`
      case 'reference':
        return `/references/${result.slug}`
      case 'quiz':
        return `/quizzes/${result.slug}`
      case 'exercise':
        return `/exercises/${result.slug}`
      default:
        return '#'
    }
  }

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-purple-100 dark:bg-purple-900 rounded-full">
              <Search className="h-12 w-12 text-purple-600" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Search GuruDevs
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto">
            Find tutorials, references, quizzes, and exercises across all programming languages and
            topics.
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-2xl mx-auto">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="Search for tutorials, references, quizzes..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="pl-10 pr-4 py-3 text-lg"
            />
          </div>
        </div>

        {/* Filters */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-4 justify-center">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Type:</span>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm"
              >
                <option value="all">All Types</option>
                <option value="tutorial">Tutorials</option>
                <option value="reference">References</option>
                <option value="quiz">Quizzes</option>
                <option value="exercise">Exercises</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Difficulty:
              </span>
              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-sm"
              >
                <option value="all">All Levels</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
          </div>
        </div>

        {/* Search Results */}
        <div className="space-y-6">
          {isLoading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-600 mx-auto"></div>
              <p className="text-gray-600 dark:text-gray-400 mt-2">Searching...</p>
            </div>
          )}

          {!isLoading && query && filteredResults.length === 0 && (
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No results found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Try adjusting your search terms or filters.
              </p>
              <Button onClick={() => setQuery('')} variant="outline">
                Clear Search
              </Button>
            </div>
          )}

          {!isLoading && query && filteredResults.length > 0 && (
            <>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Search Results
                </h2>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {filteredResults.length} result{filteredResults.length !== 1 ? 's' : ''} found
                </span>
              </div>

              <div className="space-y-4">
                {filteredResults.map((result) => {
                  const Icon = typeIcons[result.type]
                  return (
                    <Link key={result.id} href={getResultUrl(result)}>
                      <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                        <CardHeader>
                          <div className="flex items-start justify-between">
                            <div className="flex items-center gap-3 mb-2">
                              <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                                <Icon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                              </div>
                              <Badge className={typeColors[result.type]}>{result.type}</Badge>
                              <Badge
                                variant="secondary"
                                style={{
                                  backgroundColor: result.category.color + '20',
                                  color: result.category.color,
                                }}
                              >
                                {result.category.title}
                              </Badge>
                              {result.difficulty && (
                                <Badge className={difficultyColors[result.difficulty]}>
                                  {result.difficulty}
                                </Badge>
                              )}
                            </div>
                            <ArrowRight className="h-5 w-5 text-gray-400" />
                          </div>
                          <CardTitle className="text-lg">{result.title}</CardTitle>
                          <CardDescription>{result.description}</CardDescription>
                        </CardHeader>
                      </Card>
                    </Link>
                  )
                })}
              </div>
            </>
          )}

          {!query && (
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Start Searching
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Enter a search term to find tutorials, references, quizzes, and exercises.
              </p>
              <div className="flex flex-wrap gap-2 justify-center">
                <Button variant="outline" size="sm" onClick={() => setQuery('HTML')}>
                  HTML
                </Button>
                <Button variant="outline" size="sm" onClick={() => setQuery('CSS')}>
                  CSS
                </Button>
                <Button variant="outline" size="sm" onClick={() => setQuery('JavaScript')}>
                  JavaScript
                </Button>
                <Button variant="outline" size="sm" onClick={() => setQuery('Python')}>
                  Python
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
