import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import Link from 'next/link'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText, Users, Shield, AlertTriangle, Scale, Globe } from 'lucide-react'

export default function TermsPage() {
  const lastUpdated = 'December 2024'

  const sections = [
    {
      icon: Users,
      title: 'Acceptance of Terms',
      content: [
        'By accessing and using GuruDevs, you accept and agree to be bound by these Terms of Service.',
        'If you do not agree to these terms, please do not use our website or services.',
        'These terms apply to all visitors, users, and others who access or use our service.',
        'We reserve the right to update these terms at any time without prior notice.',
      ],
    },
    {
      icon: Globe,
      title: 'Use of Service',
      content: [
        'GuruDevs provides free educational content for learning programming languages.',
        'You may use our service for personal, non-commercial educational purposes.',
        'You agree not to use our service for any unlawful or prohibited activities.',
        'You are responsible for maintaining the confidentiality of any account information.',
        'You agree not to reproduce, distribute, or create derivative works from our content without permission.',
      ],
    },
    {
      icon: Shield,
      title: 'User Conduct',
      content: [
        'You agree to use GuruDevs in a manner consistent with all applicable laws and regulations.',
        "You will not attempt to gain unauthorized access to our systems or other users' accounts.",
        'You will not upload, post, or transmit any harmful, offensive, or illegal content.',
        'You will not interfere with or disrupt the service or servers connected to the service.',
        'You will not use automated systems to access the service without our permission.',
      ],
    },
    {
      icon: FileText,
      title: 'Intellectual Property',
      content: [
        'All content on GuruDevs, including text, graphics, logos, and software, is our property or licensed to us.',
        'Our content is protected by copyright, trademark, and other intellectual property laws.',
        'You may not copy, modify, distribute, or reverse engineer any part of our service.',
        'You retain ownership of any content you submit to us, but grant us a license to use it.',
        'We respect the intellectual property rights of others and expect users to do the same.',
      ],
    },
    {
      icon: AlertTriangle,
      title: 'Disclaimers',
      content: [
        'GuruDevs is provided "as is" without warranties of any kind, express or implied.',
        'We do not guarantee that our service will be uninterrupted, secure, or error-free.',
        'We are not responsible for any damages resulting from your use of our service.',
        'The information on our site is for educational purposes and may not be completely accurate or up-to-date.',
        'We do not endorse or guarantee any third-party content or services linked from our site.',
      ],
    },
    {
      icon: Scale,
      title: 'Limitation of Liability',
      content: [
        'In no event shall GuruDevs be liable for any indirect, incidental, or consequential damages.',
        'Our total liability to you for any claims shall not exceed the amount you paid us (which is $0 for free services).',
        'Some jurisdictions do not allow the exclusion of certain warranties or limitations of liability.',
        'These limitations apply to the fullest extent permitted by applicable law.',
        'You agree to indemnify and hold us harmless from any claims arising from your use of our service.',
      ],
    },
  ]

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-blue-100 dark:bg-blue-900 rounded-full">
              <Scale className="h-12 w-12 text-blue-600" />
            </div>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Terms of Service
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto">
            These terms govern your use of GuruDevs and outline the rights and responsibilities of
            both users and our platform.
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
            Last updated: {lastUpdated}
          </p>
        </div>

        {/* Introduction */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              Welcome to GuruDevs! These Terms of Service (&quot;Terms&quot;) govern your use of our
              website and educational services. By using GuruDevs, you agree to comply with and be
              bound by these terms. Please read them carefully.
            </p>
          </CardContent>
        </Card>

        {/* Terms Sections */}
        <div className="space-y-8">
          {sections.map((section, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                    <section.icon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                  </div>
                  {section.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {section.content.map((item, itemIndex) => (
                    <li
                      key={itemIndex}
                      className="text-gray-700 dark:text-gray-300 leading-relaxed"
                    >
                      • {item}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Privacy Policy Reference */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Privacy Policy</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              Your privacy is important to us. Please review our{' '}
              <Link
                href="/privacy-policy"
                className="text-brand-600 hover:text-brand-700 underline"
              >
                Privacy Policy
              </Link>
              , which also governs your use of GuruDevs, to understand our practices regarding the
              collection and use of your information.
            </p>
          </CardContent>
        </Card>

        {/* Termination */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Termination</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-gray-700 dark:text-gray-300 leading-relaxed">
              <p>
                We may terminate or suspend your access to GuruDevs immediately, without prior
                notice or liability, for any reason, including if you breach these Terms.
              </p>
              <p>
                Upon termination, your right to use the service will cease immediately. All
                provisions of these Terms that should survive termination shall survive, including
                ownership provisions, warranty disclaimers, and limitations of liability.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Governing Law */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Governing Law</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
              These Terms shall be governed by and construed in accordance with the laws of the
              jurisdiction in which GuruDevs operates, without regard to conflict of law provisions.
              Any disputes arising from these terms or your use of our service shall be resolved
              through binding arbitration or in the courts of our jurisdiction.
            </p>
          </CardContent>
        </Card>

        {/* Changes to Terms */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Changes to Terms</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-gray-700 dark:text-gray-300 leading-relaxed">
              <p>
                We reserve the right to modify or replace these Terms at any time. If a revision is
                material, we will try to provide at least 30 days notice prior to any new terms
                taking effect.
              </p>
              <p>
                What constitutes a material change will be determined at our sole discretion. By
                continuing to access or use our service after those revisions become effective, you
                agree to be bound by the revised terms.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card className="mt-8 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
          <CardHeader>
            <CardTitle className="text-blue-800 dark:text-blue-200">
              Questions About These Terms?
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-blue-700 dark:text-blue-300 leading-relaxed mb-4">
              If you have any questions about these Terms of Service, please contact us:
            </p>
            <div className="space-y-2 text-blue-700 dark:text-blue-300">
              <p>• Email: <EMAIL></p>
              <p>
                • Contact Form:{' '}
                <Link href="/contact" className="underline hover:no-underline">
                  gurudevs.com/contact
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Effective Date */}
        <div className="text-center mt-12 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            These Terms of Service are effective as of {lastUpdated} and apply to all users of
            GuruDevs.
          </p>
        </div>
      </div>
    </div>
  )
}

export const metadata: Metadata = {
  title: 'Terms of Service | GuruDevs',
  description:
    'Read the Terms of Service for GuruDevs. Understand your rights and responsibilities when using our educational platform.',
  openGraph: {
    title: 'Terms of Service | GuruDevs',
    description: 'Read the Terms of Service for GuruDevs educational platform.',
    type: 'website',
  },
}
