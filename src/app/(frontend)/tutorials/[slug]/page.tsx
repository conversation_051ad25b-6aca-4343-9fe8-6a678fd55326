import type { <PERSON>ada<PERSON> } from 'next'
import React from 'react'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { notFound } from 'next/navigation'
import MarkdownRichText from '@/components/MarkdownRichText'
import { Badge } from '@/components/ui/badge'
import { Clock } from 'lucide-react'
import { TutorialLayout } from '@/components/TutorialLayout'
import { getMeUser } from '@/utilities/getMeUser'

interface TutorialPageProps {
  params: Promise<{
    slug: string
  }>
}

async function getTutorial(slug: string) {
  const payload = await getPayload({ config: configPromise })

  try {
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        slug: {
          equals: slug,
        },
        _status: {
          equals: 'published',
        },
      },
      limit: 1,
      depth: 2,
      select: {
        title: true,
        slug: true,
        excerpt: true,
        content: true,
        category: true,
        chapter: true,
        lessonNumber: true,
        difficulty: true,
        estimatedTime: true,
        previousTutorial: true,
        nextTutorial: true,
        relatedTutorials: true,
      },
    })

    if (tutorials.docs.length === 0) {
      return null
    }

    const tutorial = tutorials.docs[0]

    // Debug logging
    console.log('Fetched tutorial:', {
      id: tutorial?.id,
      title: tutorial?.title,
      hasContent: !!tutorial?.content,
      contentType: typeof tutorial?.content,
      contentKeys: tutorial?.content ? Object.keys(tutorial.content) : 'No content',
    })

    // Get all chapters and their tutorials for the sidebar (W3Schools style)
    const categoryId =
      typeof tutorial?.category === 'object' ? tutorial?.category?.id : tutorial?.category

    // First, get all chapters in this category
    const chapters = await payload.find({
      collection: 'chapters',
      where: {
        category: {
          equals: categoryId,
        },
        _status: {
          equals: 'published',
        },
      },
      sort: 'order',
      limit: 50,
      depth: 1,
    })

    // Then, get all tutorials for each chapter
    const chaptersWithTutorials = await Promise.all(
      chapters.docs.map(async (chapter: any) => {
        const chapterTutorials = await payload.find({
          collection: 'tutorials',
          where: {
            chapter: {
              equals: chapter.id,
            },
            _status: {
              equals: 'published',
            },
          },
          sort: 'lessonNumber',
          limit: 50,
          select: {
            title: true,
            slug: true,
            lessonNumber: true,
          },
        })

        return {
          id: chapter.id,
          title: chapter.title,
          slug: chapter.slug,
          order: chapter.order,
          tutorials: chapterTutorials.docs.map((t: any) => ({
            title: t.title,
            slug: t.slug,
            lessonNumber: t.lessonNumber || 1,
            isActive: t.slug === slug,
          })),
        }
      }),
    )

    // Also get tutorials that don't belong to any chapter (standalone tutorials)
    const standaloneTutorials = await payload.find({
      collection: 'tutorials',
      where: {
        and: [
          {
            category: {
              equals: categoryId,
            },
          },
          {
            chapter: {
              exists: false,
            },
          },
          {
            _status: {
              equals: 'published',
            },
          },
        ],
      },
      sort: 'order',
      limit: 50,
      select: {
        title: true,
        slug: true,
      },
    })

    // Create sidebar items with chapter-based structure
    const sidebarItems = []

    // Add chapters with their tutorials
    if (chaptersWithTutorials.length > 0) {
      chaptersWithTutorials
        .sort((a, b) => a.order - b.order)
        .forEach((chapter) => {
          sidebarItems.push({
            title: `Chapter ${chapter.order}: ${chapter.title}`,
            slug: chapter.slug,
            isChapter: true,
            children: chapter.tutorials.map((tutorial) => ({
              title: `${tutorial.lessonNumber}. ${tutorial.title}`,
              slug: tutorial.slug,
              isActive: tutorial.isActive,
            })),
          })
        })
    }

    // Add standalone tutorials if any
    if (standaloneTutorials.docs.length > 0) {
      sidebarItems.push({
        title: 'Additional Tutorials',
        slug: '',
        isChapter: true,
        children: standaloneTutorials.docs.map((t: any) => ({
          title: t.title,
          slug: t.slug,
          isActive: t.slug === slug,
        })),
      })
    }

    // If no chapters exist, create a demo structure for testing
    if (sidebarItems.length === 0) {
      sidebarItems.push(
        {
          title: 'Chapter 1: MongoDB Fundamentals',
          slug: 'mongodb-fundamentals',
          isChapter: true,
          children: [
            {
              title: '1. What is MongoDB? Features and Use Cases',
              slug: tutorial?.slug || slug,
              isActive: tutorial?.slug === slug,
            },
            {
              title: '2. MongoDB vs SQL Databases (Key Differences)',
              slug: 'mongodb-vs-sql',
              isActive: false,
            },
            {
              title: '3. MongoDB Architecture Overview',
              slug: 'mongodb-architecture',
              isActive: false,
            },
            {
              title: '4. Installing MongoDB (Windows, macOS, Linux)',
              slug: 'installing-mongodb',
              isActive: false,
            },
          ],
        },
        {
          title: 'Chapter 2: Getting Started with MongoDB',
          slug: 'getting-started-mongodb',
          isChapter: true,
          children: [
            {
              title: '1. Databases, Collections, Documents: Core Concepts',
              slug: 'mongodb-core-concepts',
              isActive: false,
            },
            {
              title: '2. BSON and Supported Data Types',
              slug: 'mongodb-bson-data-types',
              isActive: false,
            },
            {
              title: '3. Connecting to Local and Cloud (Atlas) Instances',
              slug: 'mongodb-connections',
              isActive: false,
            },
            {
              title: '4. CRUD Operations: Insert, Find, Update, Delete',
              slug: 'mongodb-crud-operations',
              isActive: false,
            },
          ],
        },
        {
          title: 'Chapter 3: Querying Documents in MongoDB',
          slug: 'querying-mongodb',
          isChapter: true,
          children: [
            {
              title: '1. Query Operators: $eq, $gt, $in, $regex, $or, $and',
              slug: 'mongodb-query-operators',
              isActive: false,
            },
            {
              title: '2. Working with Arrays and Embedded Documents',
              slug: 'mongodb-arrays-embedded',
              isActive: false,
            },
            {
              title: '3. Sorting, Projection, and Pagination',
              slug: 'mongodb-sorting-projection',
              isActive: false,
            },
            {
              title: '4. Full-Text Search Basics',
              slug: 'mongodb-text-search',
              isActive: false,
            },
            {
              title: '5. Optimizing Query Performance',
              slug: 'mongodb-query-optimization',
              isActive: false,
            },
          ],
        },
      )
    }

    // Calculate next and previous lessons for navigation
    let previousLesson = null
    let nextLesson = null

    // Find current lesson in the sidebar structure and get adjacent lessons
    for (let i = 0; i < sidebarItems.length; i++) {
      const chapter = sidebarItems[i]
      if (chapter?.children) {
        for (let j = 0; j < chapter.children.length; j++) {
          const lesson = chapter.children[j]

          if (lesson?.isActive) {
            // Found current lesson, now find previous and next

            // Previous lesson
            if (j > 0) {
              // Previous lesson in same chapter
              previousLesson = chapter.children[j - 1]
            } else if (i > 0) {
              // Last lesson of previous chapter
              const prevChapter = sidebarItems[i - 1]
              if (prevChapter?.children && prevChapter.children.length > 0) {
                previousLesson = prevChapter.children[prevChapter.children.length - 1]
              }
            }

            // Next lesson
            if (j < chapter.children.length - 1) {
              // Next lesson in same chapter
              nextLesson = chapter.children[j + 1]
            } else if (i < sidebarItems.length - 1) {
              // First lesson of next chapter
              const nextChapter = sidebarItems[i + 1]
              if (nextChapter?.children && nextChapter.children.length > 0) {
                nextLesson = nextChapter.children[0]
              }
            }

            break
          }
        }
        if (previousLesson !== null || nextLesson !== null) break
      }
    }

    return {
      tutorial,
      sidebarItems,
      previousLesson,
      nextLesson,
    }
  } catch (error) {
    console.error('Error fetching tutorial:', error)
    return null
  }
}

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })

  try {
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
      },
    })

    return tutorials.docs.map((tutorial: any) => ({
      slug: tutorial.slug,
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

export default async function TutorialPage({ params }: TutorialPageProps) {
  const { slug } = await params
  const data = await getTutorial(slug)

  if (!data) {
    notFound()
  }

  const { tutorial, sidebarItems, previousLesson, nextLesson } = data

  // Check if user is admin
  let isAdmin = false
  try {
    const { user } = await getMeUser()
    isAdmin = Boolean(user?.role === 'admin') // Check for admin role specifically
    console.log('Admin check:', { userId: user?.id, userRole: user?.role, isAdmin })
  } catch (error) {
    // User is not authenticated, isAdmin remains false
    // This is expected for non-logged-in users
    console.log('User not authenticated:', error instanceof Error ? error.message : error)
    isAdmin = false
  }

  // Generate edit URL
  const editUrl = tutorial?.id
    ? `/admin/collections/tutorials/${tutorial.id}`
    : '/admin/collections/tutorials/create'

  console.log('Edit URL generated:', editUrl, 'Tutorial ID:', tutorial?.id)

  // Transform tutorial data to match TutorialLayout interface
  const tutorialData = {
    id: tutorial?.id || '',
    title: tutorial?.title || '',
    excerpt: tutorial?.excerpt || '',
    content: tutorial?.content || null,
    category:
      tutorial?.category && typeof tutorial.category === 'object'
        ? {
            title: (tutorial.category as any).title || 'General',
            slug: (tutorial.category as any).slug || 'general',
            color: (tutorial.category as any).color || '#3B82F6',
          }
        : {
            title: 'General',
            slug: 'general',
            color: '#3B82F6',
          },
    difficulty: (tutorial?.difficulty as 'beginner' | 'intermediate' | 'advanced') || 'beginner',
    estimatedTime: tutorial?.estimatedTime || '',
    previousTutorial:
      tutorial?.previousTutorial && typeof tutorial.previousTutorial === 'object'
        ? {
            title: (tutorial.previousTutorial as any).title || '',
            slug: (tutorial.previousTutorial as any).slug || '',
          }
        : undefined,
    nextTutorial:
      tutorial?.nextTutorial && typeof tutorial.nextTutorial === 'object'
        ? {
            title: (tutorial.nextTutorial as any).title || '',
            slug: (tutorial.nextTutorial as any).slug || '',
          }
        : undefined,
    relatedTutorials: Array.isArray(tutorial?.relatedTutorials)
      ? tutorial.relatedTutorials.map((related: any) => ({
          title: related?.title || '',
          slug: related?.slug || '',
          excerpt: related?.excerpt || '',
        }))
      : [],
  }

  return (
    <TutorialLayout
      tutorial={tutorialData}
      sidebarItems={sidebarItems}
      previousLesson={previousLesson}
      nextLesson={nextLesson}
      isAdmin={isAdmin}
      editUrl={editUrl}
    />
  )
}

export async function generateMetadata({ params }: TutorialPageProps): Promise<Metadata> {
  const { slug } = await params
  const data = await getTutorial(slug)

  if (!data) {
    return {
      title: 'Tutorial Not Found | GuruDevs',
    }
  }

  const { tutorial } = data

  return {
    title: `${tutorial?.title || 'Tutorial'} | GuruDevs`,
    description:
      tutorial?.excerpt ||
      `Learn ${tutorial?.title || 'programming'} with our comprehensive tutorial.`,
    openGraph: {
      title: tutorial?.title || 'Tutorial',
      description:
        tutorial?.excerpt ||
        `Learn ${tutorial?.title || 'programming'} with our comprehensive tutorial.`,
      type: 'article',
    },
  }
}
