import type { Metadata } from 'next'
import React from 'react'
import { TutorialLayout } from '@/components/TutorialLayout'

// Mock tutorial data for the second lesson
const mockTutorial = {
  id: 'mongodb-vs-sql',
  title: 'MongoDB vs SQL Databases (Key Differences)',
  excerpt: 'Understand the key differences between MongoDB and traditional SQL databases.',
  content: {
    root: {
      type: 'root',
      children: [
        {
          type: 'heading',
          children: [{ text: 'MongoDB vs SQL Databases' }],
          tag: 'h2',
        },
        {
          type: 'paragraph',
          children: [
            {
              text: 'MongoDB and SQL databases serve different purposes and have distinct characteristics.',
            },
          ],
        },
        {
          type: 'heading',
          children: [{ text: 'Key Differences' }],
          tag: 'h3',
        },
        {
          type: 'list',
          children: [
            {
              type: 'listItem',
              children: [
                {
                  type: 'paragraph',
                  children: [{ text: 'Data Model: MongoDB uses documents, SQL uses tables' }],
                },
              ],
            },
            {
              type: 'listItem',
              children: [
                {
                  type: 'paragraph',
                  children: [{ text: 'Schema: MongoDB is schema-flexible, SQL is schema-rigid' }],
                },
              ],
            },
            {
              type: 'listItem',
              children: [
                {
                  type: 'paragraph',
                  children: [{ text: 'Query Language: MongoDB uses MQL, SQL uses SQL' }],
                },
              ],
            },
            {
              type: 'listItem',
              children: [
                {
                  type: 'paragraph',
                  children: [
                    {
                      text: 'Scalability: MongoDB scales horizontally, SQL traditionally scales vertically',
                    },
                  ],
                },
              ],
            },
          ],
          tag: 'ul',
        },
        {
          type: 'heading',
          children: [{ text: 'When to Use MongoDB' }],
          tag: 'h3',
        },
        {
          type: 'list',
          children: [
            {
              type: 'listItem',
              children: [
                {
                  type: 'paragraph',
                  children: [{ text: 'Rapid application development with changing requirements' }],
                },
              ],
            },
            {
              type: 'listItem',
              children: [
                {
                  type: 'paragraph',
                  children: [{ text: 'Working with unstructured or semi-structured data' }],
                },
              ],
            },
            {
              type: 'listItem',
              children: [
                {
                  type: 'paragraph',
                  children: [{ text: 'Need for horizontal scaling and high availability' }],
                },
              ],
            },
          ],
          tag: 'ul',
        },
      ],
    },
  },
  category: {
    title: 'MongoDB',
    slug: 'mongodb',
    color: '#4DB33D',
  },
  difficulty: 'beginner' as const,
  estimatedTime: '25 minutes',
}

// Same sidebar structure as the first lesson
const mockSidebarItems = [
  {
    title: 'Chapter 1: MongoDB Fundamentals',
    slug: 'mongodb-fundamentals',
    isChapter: true,
    children: [
      {
        title: '1. What is MongoDB? Features and Use Cases',
        slug: 'test-mongodb',
        isActive: false,
      },
      {
        title: '2. MongoDB vs SQL Databases (Key Differences)',
        slug: 'mongodb-vs-sql',
        isActive: true, // This lesson is active
      },
      {
        title: '3. MongoDB Architecture Overview (Replica Set, Sharding)',
        slug: 'mongodb-architecture',
        isActive: false,
      },
      {
        title: '4. MongoDB Version History and Compatibility',
        slug: 'mongodb-version-history',
        isActive: false,
      },
      {
        title: '5. Installing MongoDB (Windows, macOS, Linux)',
        slug: 'installing-mongodb',
        isActive: false,
      },
      {
        title: '6. MongoDB Tools Overview: Compass, mongosh, Atlas',
        slug: 'mongodb-tools',
        isActive: false,
      },
    ],
  },
  {
    title: 'Chapter 2: Getting Started with MongoDB',
    slug: 'getting-started-mongodb',
    isChapter: true,
    children: [
      {
        title: '1. Databases, Collections, Documents: Core Concepts',
        slug: 'mongodb-core-concepts',
        isActive: false,
      },
      {
        title: '2. BSON and Supported Data Types',
        slug: 'mongodb-bson-data-types',
        isActive: false,
      },
      {
        title: '3. Connecting to Local and Cloud (Atlas) Instances',
        slug: 'mongodb-connections',
        isActive: false,
      },
      {
        title: '4. Using MongoDB Shell (mongosh) and MongoDB Compass',
        slug: 'mongodb-shell-compass',
        isActive: false,
      },
      {
        title: '5. CRUD Operations: Insert, Find, Update, Delete',
        slug: 'mongodb-crud-operations',
        isActive: false,
      },
    ],
  },
  {
    title: 'Chapter 3: Querying Documents in MongoDB',
    slug: 'querying-mongodb',
    isChapter: true,
    children: [
      {
        title: '1. Query Operators: $eq, $gt, $in, $regex, $or, $and, etc.',
        slug: 'mongodb-query-operators',
        isActive: false,
      },
      {
        title: '2. Working with Arrays and Embedded Documents',
        slug: 'mongodb-arrays-embedded',
        isActive: false,
      },
      {
        title: '3. Sorting, Projection, and Pagination',
        slug: 'mongodb-sorting-projection',
        isActive: false,
      },
      {
        title: '4. Full-Text Search Basics',
        slug: 'mongodb-text-search',
        isActive: false,
      },
      {
        title: '5. Optimizing Query Performance',
        slug: 'mongodb-query-optimization',
        isActive: false,
      },
    ],
  },
]

export default function MongoDBvsSQLPage() {
  // Navigation for the second lesson
  const previousLesson = {
    title: '1. What is MongoDB? Features and Use Cases',
    slug: 'test-mongodb',
    isActive: false,
  }

  const nextLesson = {
    title: '3. MongoDB Architecture Overview (Replica Set, Sharding)',
    slug: 'mongodb-architecture',
    isActive: false,
  }

  return (
    <TutorialLayout
      tutorial={mockTutorial}
      sidebarItems={mockSidebarItems}
      previousLesson={previousLesson}
      nextLesson={nextLesson}
      isAdmin={true} // For demo purposes, show edit button
      editUrl="/admin/collections/tutorials/create" // Create new tutorial since this is a demo
    />
  )
}

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'MongoDB vs SQL Databases (Key Differences) | GuruDevs',
    description: 'Understand the key differences between MongoDB and traditional SQL databases.',
    openGraph: {
      title: 'MongoDB vs SQL Databases (Key Differences)',
      description: 'Understand the key differences between MongoDB and traditional SQL databases.',
      type: 'article',
    },
  }
}
