import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import Link from 'next/link'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { BookO<PERSON>, Clock, ArrowRight } from 'lucide-react'

interface Tutorial {
  id: string
  title: string
  excerpt?: string
  slug: string
  category: {
    title: string
    slug: string
    color?: string
  }
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime?: string
}

interface Category {
  id: string
  title: string
  slug: string
  color?: string
  description?: string
}

const difficultyColors = {
  beginner: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  advanced: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
}

async function getTutorials() {
  const payload = await getPayload({ config: configPromise })

  try {
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        _status: {
          equals: 'published',
        },
      },
      sort: 'order',
      limit: 50,
      depth: 2,
    })

    const categories = await payload.find({
      collection: 'categories',
      where: {
        featured: {
          equals: true,
        },
      },
      sort: 'order',
      limit: 20,
    })

    return {
      tutorials: tutorials.docs,
      categories: categories.docs,
    }
  } catch (error) {
    console.error('Error fetching tutorials:', error)
    return {
      tutorials: [],
      categories: [],
    }
  }
}

export default async function TutorialsPage() {
  const { tutorials, categories } = await getTutorials()

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Programming Tutorials
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Learn programming step by step with our comprehensive tutorials. From beginner to
            advanced, we&apos;ve got you covered.
          </p>
        </div>

        {/* Categories */}
        {categories.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              Browse by Category
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {categories.map((category: any) => (
                <Link key={category.id} href={`/tutorials/${category.slug}`}>
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                    <CardHeader className="text-center">
                      <div
                        className="w-12 h-12 mx-auto rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform"
                        style={{ backgroundColor: category.color || '#3B82F6' }}
                      >
                        <BookOpen className="h-6 w-6 text-white" />
                      </div>
                      <CardTitle className="text-lg">{category.title}</CardTitle>
                      {category.description && (
                        <CardDescription className="text-sm">
                          {category.description}
                        </CardDescription>
                      )}
                    </CardHeader>
                  </Card>
                </Link>
              ))}
            </div>
          </section>
        )}

        {/* All Tutorials */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">All Tutorials</h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {tutorials.length} tutorials available
              </span>
            </div>
          </div>

          {tutorials.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {tutorials.map((tutorial: any) => (
                <Link key={tutorial.id} href={`/tutorials/${tutorial.slug}`}>
                  <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-2">
                        <Badge
                          variant="secondary"
                          style={{
                            backgroundColor: tutorial.category.color || '#3B82F6' + '20',
                            color: tutorial.category.color || '#3B82F6',
                          }}
                        >
                          {tutorial.category.title}
                        </Badge>
                        <Badge
                          className={
                            difficultyColors[
                              tutorial.difficulty as keyof typeof difficultyColors
                            ] || difficultyColors.beginner
                          }
                        >
                          {tutorial.difficulty}
                        </Badge>
                      </div>
                      <CardTitle className="text-lg line-clamp-2">{tutorial.title}</CardTitle>
                      {tutorial.excerpt && (
                        <CardDescription className="line-clamp-3">
                          {tutorial.excerpt}
                        </CardDescription>
                      )}
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between">
                        {tutorial.estimatedTime && (
                          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <Clock className="h-4 w-4 mr-1" />
                            {tutorial.estimatedTime}
                          </div>
                        )}
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No tutorials available yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                We&apos;re working on creating amazing tutorials for you. Check back soon!
              </p>
              <Button asChild>
                <Link href="/">Back to Home</Link>
              </Button>
            </div>
          )}
        </section>
      </div>
    </div>
  )
}

export const metadata: Metadata = {
  title: 'Programming Tutorials | GuruDevs',
  description:
    'Learn programming with our comprehensive tutorials. From HTML and CSS to JavaScript and Python, master coding step by step.',
}
