import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import Link from 'next/link'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { BookOpen, Clock, ArrowRight, ChevronRight, Users, Target, Layers } from 'lucide-react'

interface Chapter {
  id: string
  title: string
  description?: string
  slug: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  order: number
  estimatedTime?: string
  tutorialCount: number
  firstTutorialSlug?: string
}

interface Category {
  id: string
  title: string
  slug: string
  color?: string
  description?: string
  chapters: Chapter[]
  totalTutorials: number
}

const difficultyColors = {
  beginner: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  advanced: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
}

async function getCategoriesWithChapters() {
  const payload = await getPayload({ config: configPromise })

  try {
    // Get all categories
    const categories = await payload.find({
      collection: 'categories',
      where: {
        featured: {
          equals: true,
        },
      },
      sort: 'order',
      limit: 20,
    })

    // For each category, get its chapters and tutorials
    const categoriesWithChapters = await Promise.all(
      categories.docs.map(async (category: any) => {
        // Get chapters for this category
        const chapters = await payload.find({
          collection: 'chapters',
          where: {
            category: {
              equals: category.id,
            },
            _status: {
              equals: 'published',
            },
          },
          sort: 'order',
          limit: 50,
        })

        // For each chapter, get tutorial count and first tutorial
        const chaptersWithTutorials = await Promise.all(
          chapters.docs.map(async (chapter: any) => {
            const tutorials = await payload.find({
              collection: 'tutorials',
              where: {
                chapter: {
                  equals: chapter.id,
                },
                _status: {
                  equals: 'published',
                },
              },
              sort: 'lessonNumber',
              limit: 1000,
            })

            const firstTutorial = tutorials.docs[0]

            return {
              id: chapter.id,
              title: chapter.title,
              description: chapter.description,
              slug: chapter.slug,
              difficulty: chapter.difficulty || 'beginner',
              order: chapter.order || 0,
              estimatedTime: chapter.estimatedTime,
              tutorialCount: tutorials.totalDocs,
              firstTutorialSlug: firstTutorial?.slug,
            }
          }),
        )

        // Get total tutorials for this category
        const allTutorials = await payload.find({
          collection: 'tutorials',
          where: {
            category: {
              equals: category.id,
            },
            _status: {
              equals: 'published',
            },
          },
          limit: 1000,
          pagination: false,
        })

        return {
          id: category.id,
          title: category.title,
          slug: category.slug,
          color: category.color || '#3B82F6',
          description: category.description,
          chapters: chaptersWithTutorials.filter((chapter) => chapter.tutorialCount > 0),
          totalTutorials: allTutorials.totalDocs,
        }
      }),
    )

    return categoriesWithChapters.filter((category) => category.totalTutorials > 0)
  } catch (error) {
    console.error('Error fetching categories with chapters:', error)
    return []
  }
}

export const metadata: Metadata = {
  title: 'Programming Tutorials | GuruDevs',
  description:
    'Learn programming with our structured chapter-based tutorials. Choose from HTML, CSS, JavaScript, Python and more. Each chapter contains multiple lessons to guide you step by step.',
}

export default async function TutorialsPage() {
  const categories = await getCategoriesWithChapters()

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Programming Tutorials
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Choose a programming language and start learning with our structured chapter-based
            tutorials. Each chapter contains multiple lessons to guide you step by step.
          </p>
        </div>

        {categories.length > 0 ? (
          <div className="space-y-12">
            {categories.map((category) => (
              <section key={category.id} className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div
                      className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg"
                      style={{
                        backgroundColor: category.color,
                        boxShadow: `0 10px 25px ${category.color}20`,
                      }}
                    >
                      <BookOpen className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
                        {category.title}
                      </h2>
                      {category.description && (
                        <p className="text-gray-600 dark:text-gray-300 mt-1">
                          {category.description}
                        </p>
                      )}
                      <div className="flex items-center space-x-4 mt-2">
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <Layers className="h-4 w-4 mr-1" />
                          {category.chapters.length} chapters
                        </div>
                        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          <Target className="h-4 w-4 mr-1" />
                          {category.totalTutorials} lessons
                        </div>
                      </div>
                    </div>
                  </div>
                  <Button variant="outline" asChild>
                    <Link href={`/courses/${category.slug}`}>
                      View Course
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {category.chapters.map((chapter) => (
                    <Link
                      key={chapter.id}
                      href={
                        chapter.firstTutorialSlug
                          ? `/tutorials/${chapter.firstTutorialSlug}`
                          : `/chapters/${chapter.slug}`
                      }
                    >
                      <Card
                        className="h-full hover:shadow-xl transition-all duration-300 cursor-pointer group border-l-4 hover:-translate-y-1"
                        style={{ borderLeftColor: category.color }}
                      >
                        <CardHeader>
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2 mb-2">
                                <Badge
                                  className={
                                    difficultyColors[
                                      chapter.difficulty as keyof typeof difficultyColors
                                    ] || difficultyColors.beginner
                                  }
                                >
                                  {chapter.difficulty}
                                </Badge>
                                <Badge variant="outline">{chapter.tutorialCount} lessons</Badge>
                              </div>
                              <CardTitle className="text-xl group-hover:text-blue-600 transition-colors">
                                Chapter {chapter.order}: {chapter.title}
                              </CardTitle>
                              {chapter.description && (
                                <CardDescription className="mt-2 line-clamp-3">
                                  {chapter.description}
                                </CardDescription>
                              )}
                            </div>
                            <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all flex-shrink-0 ml-2" />
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center justify-between">
                            {chapter.estimatedTime && (
                              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                <Clock className="h-4 w-4 mr-1" />
                                {chapter.estimatedTime}
                              </div>
                            )}
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              Start Learning →
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </section>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
                <BookOpen className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                No Tutorials Available Yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-8">
                We&apos;re working hard to create amazing programming tutorials for you. Check back
                soon or explore our other content!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <Link href="/">
                    <BookOpen className="mr-2 h-4 w-4" />
                    Back to Home
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/posts">
                    <Users className="mr-2 h-4 w-4" />
                    Read Blog Posts
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
