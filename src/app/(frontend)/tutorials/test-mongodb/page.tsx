import type { Metadata } from 'next'
import React from 'react'
import { TutorialLayout } from '@/components/TutorialLayout'

// Mock tutorial data for demonstration
const mockTutorial = {
  id: 'test-mongodb',
  title: 'What is MongoDB? Features and Use Cases',
  excerpt: 'Learn what MongoDB is, its key features, and when to use it in your applications.',
  content: {
    root: {
      type: 'root',
      children: [
        {
          type: 'heading',
          children: [{ text: 'Learn MongoDB' }],
          tag: 'h2',
        },
        {
          type: 'paragraph',
          children: [
            {
              text: 'MongoDB is a widely used NoSQL database management system.',
            },
          ],
        },
        {
          type: 'paragraph',
          children: [{ text: 'MongoDB is free and open-source.' }],
        },
        {
          type: 'paragraph',
          children: [{ text: 'MongoDB is ideal for both small and large applications.' }],
        },
        {
          type: 'heading',
          children: [{ text: 'Examples in Each Chapter' }],
          tag: 'h2',
        },
        {
          type: 'paragraph',
          children: [
            {
              text: 'With our online MongoDB editor, you can edit the MongoDB statements, and click on a button to view the result.',
            },
          ],
        },
        {
          type: 'heading',
          children: [{ text: 'Example' }],
          tag: 'h3',
        },
        {
          type: 'code',
          children: [
            {
              text: 'db.customers.find()',
            },
          ],
        },
        {
          type: 'paragraph',
          children: [
            {
              text: 'Click on the "Try it Yourself" button to see how it works.',
            },
          ],
        },
        {
          type: 'heading',
          children: [{ text: 'MongoDB Exercises' }],
          tag: 'h2',
        },
        {
          type: 'paragraph',
          children: [
            {
              text: 'Many chapters in this tutorial end with an exercise where you can check your level of knowledge.',
            },
          ],
        },
      ],
    },
  },
  category: {
    title: 'MongoDB',
    slug: 'mongodb',
    color: '#4DB33D',
  },
  difficulty: 'beginner' as const,
  estimatedTime: '30 minutes',
}

// Mock sidebar data showing W3Schools-like structure
const mockSidebarItems = [
  {
    title: 'Chapter 1: MongoDB Fundamentals',
    slug: 'mongodb-fundamentals',
    isChapter: true,
    children: [
      {
        title: '1. What is MongoDB? Features and Use Cases',
        slug: 'test-mongodb',
        isActive: true,
      },
      {
        title: '2. MongoDB vs SQL Databases (Key Differences)',
        slug: 'mongodb-vs-sql',
        isActive: false,
      },
      {
        title: '3. MongoDB Architecture Overview (Replica Set, Sharding)',
        slug: 'mongodb-architecture',
        isActive: false,
      },
      {
        title: '4. MongoDB Version History and Compatibility',
        slug: 'mongodb-version-history',
        isActive: false,
      },
      {
        title: '5. Installing MongoDB (Windows, macOS, Linux)',
        slug: 'installing-mongodb',
        isActive: false,
      },
      {
        title: '6. MongoDB Tools Overview: Compass, mongosh, Atlas',
        slug: 'mongodb-tools',
        isActive: false,
      },
    ],
  },
  {
    title: 'Chapter 2: Getting Started with MongoDB',
    slug: 'getting-started-mongodb',
    isChapter: true,
    children: [
      {
        title: '1. Databases, Collections, Documents: Core Concepts',
        slug: 'mongodb-core-concepts',
        isActive: false,
      },
      {
        title: '2. BSON and Supported Data Types',
        slug: 'mongodb-bson-data-types',
        isActive: false,
      },
      {
        title: '3. Connecting to Local and Cloud (Atlas) Instances',
        slug: 'mongodb-connections',
        isActive: false,
      },
      {
        title: '4. Using MongoDB Shell (mongosh) and MongoDB Compass',
        slug: 'mongodb-shell-compass',
        isActive: false,
      },
      {
        title: '5. CRUD Operations: Insert, Find, Update, Delete',
        slug: 'mongodb-crud-operations',
        isActive: false,
      },
    ],
  },
  {
    title: 'Chapter 3: Querying Documents in MongoDB',
    slug: 'querying-mongodb',
    isChapter: true,
    children: [
      {
        title: '1. Query Operators: $eq, $gt, $in, $regex, $or, $and, etc.',
        slug: 'mongodb-query-operators',
        isActive: false,
      },
      {
        title: '2. Working with Arrays and Embedded Documents',
        slug: 'mongodb-arrays-embedded',
        isActive: false,
      },
      {
        title: '3. Sorting, Projection, and Pagination',
        slug: 'mongodb-sorting-projection',
        isActive: false,
      },
      {
        title: '4. Full-Text Search Basics',
        slug: 'mongodb-text-search',
        isActive: false,
      },
      {
        title: '5. Optimizing Query Performance',
        slug: 'mongodb-query-optimization',
        isActive: false,
      },
    ],
  },
]

export default function TestMongoDBPage() {
  // Mock navigation - this would be the next lesson in the sequence
  const nextLesson = {
    title: '2. MongoDB vs SQL Databases (Key Differences)',
    slug: 'mongodb-vs-sql',
    isActive: false,
  }

  // No previous lesson since this is the first lesson
  const previousLesson = null

  return (
    <TutorialLayout
      tutorial={mockTutorial}
      sidebarItems={mockSidebarItems}
      previousLesson={previousLesson}
      nextLesson={nextLesson}
      isAdmin={true} // For demo purposes, show edit button
      editUrl="/admin/collections/tutorials/create" // Create new tutorial since this is a demo
    />
  )
}

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'What is MongoDB? Features and Use Cases | GuruDevs',
    description:
      'Learn what MongoDB is, its key features, and when to use it in your applications.',
    openGraph: {
      title: 'What is MongoDB? Features and Use Cases',
      description:
        'Learn what MongoDB is, its key features, and when to use it in your applications.',
      type: 'article',
    },
  }
}
