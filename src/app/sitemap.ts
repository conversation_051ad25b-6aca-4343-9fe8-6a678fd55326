import { MetadataRoute } from 'next'
import { getPayload } from 'payload'
import configPromise from '@payload-config'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const payload = await getPayload({ config: configPromise })
  const baseUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'https://gurudevs.com'

  try {
    // Static pages
    const staticPages = [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 1,
      },
      {
        url: `${baseUrl}/tutorials`,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 0.9,
      },
      {
        url: `${baseUrl}/references`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.8,
      },
      {
        url: `${baseUrl}/quizzes`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.8,
      },
      {
        url: `${baseUrl}/exercises`,
        lastModified: new Date(),
        changeFrequency: 'weekly' as const,
        priority: 0.8,
      },
      {
        url: `${baseUrl}/about`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.5,
      },
      {
        url: `${baseUrl}/contact`,
        lastModified: new Date(),
        changeFrequency: 'monthly' as const,
        priority: 0.5,
      },
      {
        url: `${baseUrl}/privacy-policy`,
        lastModified: new Date(),
        changeFrequency: 'yearly' as const,
        priority: 0.3,
      },
      {
        url: `${baseUrl}/terms`,
        lastModified: new Date(),
        changeFrequency: 'yearly' as const,
        priority: 0.3,
      },
    ]

    // Get tutorials
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
        updatedAt: true,
      },
    })

    const tutorialPages = tutorials.docs.map((tutorial: any) => ({
      url: `${baseUrl}/tutorials/${tutorial.slug}`,
      lastModified: new Date(tutorial.updatedAt),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }))

    // Get references
    const references = await payload.find({
      collection: 'references',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
        updatedAt: true,
      },
    })

    const referencePages = references.docs.map((reference: any) => ({
      url: `${baseUrl}/references/${reference.slug}`,
      lastModified: new Date(reference.updatedAt),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    }))

    // Get quizzes
    const quizzes = await payload.find({
      collection: 'quizzes',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
        updatedAt: true,
      },
    })

    const quizPages = quizzes.docs.map((quiz: any) => ({
      url: `${baseUrl}/quizzes/${quiz.slug}`,
      lastModified: new Date(quiz.updatedAt),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    }))

    // Get exercises
    const exercises = await payload.find({
      collection: 'exercises',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
        updatedAt: true,
      },
    })

    const exercisePages = exercises.docs.map((exercise: any) => ({
      url: `${baseUrl}/exercises/${exercise.slug}`,
      lastModified: new Date(exercise.updatedAt),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    }))

    // Get categories
    const categories = await payload.find({
      collection: 'categories',
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
        updatedAt: true,
      },
    })

    const categoryPages = categories.docs.map((category: any) => ({
      url: `${baseUrl}/${category.slug}`,
      lastModified: new Date(category.updatedAt),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }))

    return [
      ...staticPages,
      ...tutorialPages,
      ...referencePages,
      ...quizPages,
      ...exercisePages,
      ...categoryPages,
    ]
  } catch (error) {
    console.error('Error generating sitemap:', error)
    // Return at least the static pages if there's an error
    return [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily' as const,
        priority: 1,
      },
    ]
  }
}
