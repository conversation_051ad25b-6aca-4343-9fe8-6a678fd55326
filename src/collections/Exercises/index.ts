import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { authenticatedOrPublished } from '../../access/authenticatedOrPublished'
import { slugField } from '@/fields/slug'

import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields'

export const Exercises: CollectionConfig<'exercises'> = {
  slug: 'exercises',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  defaultPopulate: {
    title: true,
    category: true,
    difficulty: true,
  },
  admin: {
    defaultColumns: ['title', 'category', 'difficulty', 'status', 'updatedAt'],
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      admin: {
        description: 'What the user needs to accomplish in this exercise',
      },
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'categories',
      required: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'difficulty',
      type: 'select',
      options: [
        { label: 'Beginner', value: 'beginner' },
        { label: 'Intermediate', value: 'intermediate' },
        { label: 'Advanced', value: 'advanced' },
      ],
      defaultValue: 'beginner',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'type',
      type: 'select',
      options: [
        { label: 'Code Completion', value: 'code-completion' },
        { label: 'Fix the Code', value: 'fix-code' },
        { label: 'Build from Scratch', value: 'build-scratch' },
        { label: 'Multiple Choice', value: 'multiple-choice' },
      ],
      defaultValue: 'code-completion',
      required: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'language',
      type: 'select',
      options: [
        { label: 'HTML', value: 'html' },
        { label: 'CSS', value: 'css' },
        { label: 'JavaScript', value: 'javascript' },
        { label: 'Python', value: 'python' },
        { label: 'TypeScript', value: 'typescript' },
      ],
      required: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'instructions',
      type: 'richText',
      required: true,
      admin: {
        description: 'Detailed instructions for the exercise',
      },
    },
    {
      name: 'starterCode',
      type: 'code',
      admin: {
        description: 'Initial code provided to the user',
      },
    },
    {
      name: 'solutionCode',
      type: 'code',
      required: true,
      admin: {
        description: 'Complete solution code',
      },
    },
    {
      name: 'hints',
      type: 'array',
      fields: [
        {
          name: 'hint',
          type: 'textarea',
          required: true,
        },
        {
          name: 'order',
          type: 'number',
          defaultValue: 1,
        },
      ],
      admin: {
        description: 'Progressive hints to help users',
      },
    },
    {
      name: 'expectedOutput',
      type: 'textarea',
      admin: {
        description: 'What the code should output when run correctly',
      },
    },
    {
      name: 'testCases',
      type: 'array',
      fields: [
        {
          name: 'input',
          type: 'textarea',
          admin: {
            description: 'Input for the test case',
          },
        },
        {
          name: 'expectedOutput',
          type: 'textarea',
          required: true,
          admin: {
            description: 'Expected output for this test case',
          },
        },
        {
          name: 'description',
          type: 'text',
          admin: {
            description: 'Description of what this test case checks',
          },
        },
      ],
      admin: {
        description: 'Test cases to validate the solution',
      },
    },
    {
      name: 'relatedTutorials',
      type: 'relationship',
      relationTo: 'tutorials',
      hasMany: true,
      admin: {
        description: 'Tutorials that prepare for this exercise',
      },
    },
    {
      name: 'nextExercises',
      type: 'relationship',
      relationTo: 'exercises',
      hasMany: true,
      admin: {
        description: 'Suggested next exercises',
      },
    },
    {
      name: 'meta',
      label: 'SEO',
      type: 'group',
      fields: [
        OverviewField({
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
          imagePath: 'meta.image',
        }),
        MetaTitleField({
          hasGenerateFn: true,
        }),
        MetaImageField({
          relationTo: 'media',
        }),
        MetaDescriptionField({}),
        PreviewField({
          hasGenerateFn: true,
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
        }),
      ],
    },
    ...slugField(),
  ],
  versions: {
    drafts: {
      autosave: {
        interval: 100,
      },
    },
    maxPerDoc: 50,
  },
}
