import type { CollectionConfig } from 'payload'

import {
  FixedToolbarFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'
import path from 'path'
import { fileURLToPath } from 'url'

import { anyone } from '../access/anyone'
import { authenticated } from '../access/authenticated'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export const Media: CollectionConfig = {
  slug: 'media',
  access: {
    create: authenticated,
    delete: authenticated,
    read: anyone,
    update: authenticated,
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      required: true,
      admin: {
        description:
          'Alt text for accessibility and SEO (describe what the image shows). Required for all images.',
      },
    },
    {
      name: 'caption',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [...rootFeatures, FixedToolbarFeature(), InlineToolbarFeature()]
        },
      }),
      admin: {
        description: 'Optional caption that appears below the image',
      },
    },
    {
      name: 'title',
      type: 'text',
      admin: {
        description: 'Image title (appears on hover). If empty, uses filename.',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        description: 'Detailed description of the image for content management',
      },
    },
    {
      name: 'tags',
      type: 'text',
      admin: {
        description: 'Tags for organizing images (comma-separated: tutorial, html, beginner)',
      },
    },
    {
      name: 'usage',
      type: 'select',
      options: [
        { label: 'Tutorial Content', value: 'tutorial' },
        { label: 'Featured Image', value: 'featured' },
        { label: 'Social Media', value: 'social' },
        { label: 'Icon/Logo', value: 'icon' },
        { label: 'Background', value: 'background' },
        { label: 'Other', value: 'other' },
      ],
      admin: {
        description: 'How this image is intended to be used',
      },
    },
    {
      name: 'seoOptimized',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Check if this image has been optimized for web (compressed, proper format)',
      },
    },
  ],
  upload: {
    // Upload to the public/media directory in Next.js making them publicly accessible even outside of Payload
    staticDir: path.resolve(dirname, '../../public/media'),
    adminThumbnail: 'thumbnail',
    focalPoint: true,
    imageSizes: [
      {
        name: 'thumbnail',
        width: 300,
      },
      {
        name: 'square',
        width: 500,
        height: 500,
      },
      {
        name: 'small',
        width: 600,
      },
      {
        name: 'medium',
        width: 900,
      },
      {
        name: 'large',
        width: 1400,
      },
      {
        name: 'xlarge',
        width: 1920,
      },
      {
        name: 'og',
        width: 1200,
        height: 630,
        crop: 'center',
      },
    ],
  },
}
