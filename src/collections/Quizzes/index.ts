import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { authenticatedOrPublished } from '../../access/authenticatedOrPublished'
import { slugField } from '@/fields/slug'

import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields'

export const Quizzes: CollectionConfig<'quizzes'> = {
  slug: 'quizzes',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  defaultPopulate: {
    title: true,
    category: true,
    difficulty: true,
  },
  admin: {
    defaultColumns: ['title', 'category', 'difficulty', 'status', 'updatedAt'],
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        description: 'Brief description of what this quiz covers',
      },
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'categories',
      required: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'difficulty',
      type: 'select',
      options: [
        { label: 'Beginner', value: 'beginner' },
        { label: 'Intermediate', value: 'intermediate' },
        { label: 'Advanced', value: 'advanced' },
      ],
      defaultValue: 'beginner',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'timeLimit',
      type: 'number',
      admin: {
        description: 'Time limit in minutes (0 = no limit)',
        position: 'sidebar',
      },
      defaultValue: 0,
    },
    {
      name: 'passingScore',
      type: 'number',
      admin: {
        description: 'Percentage needed to pass (e.g., 70)',
        position: 'sidebar',
      },
      defaultValue: 70,
    },
    {
      name: 'questions',
      type: 'array',
      required: true,
      minRows: 1,
      fields: [
        {
          name: 'question',
          type: 'richText',
          required: true,
        },
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'Multiple Choice', value: 'multiple-choice' },
            { label: 'True/False', value: 'true-false' },
            { label: 'Fill in the Blank', value: 'fill-blank' },
          ],
          defaultValue: 'multiple-choice',
          required: true,
        },
        {
          name: 'options',
          type: 'array',
          required: true,
          minRows: 2,
          fields: [
            {
              name: 'text',
              type: 'text',
              required: true,
            },
            {
              name: 'isCorrect',
              type: 'checkbox',
              defaultValue: false,
            },
          ],
          admin: {
            condition: (_, siblingData) => 
              siblingData?.type === 'multiple-choice' || siblingData?.type === 'true-false',
          },
        },
        {
          name: 'correctAnswer',
          type: 'text',
          admin: {
            condition: (_, siblingData) => siblingData?.type === 'fill-blank',
            description: 'Correct answer for fill-in-the-blank questions',
          },
        },
        {
          name: 'explanation',
          type: 'richText',
          admin: {
            description: 'Explanation shown after answering',
          },
        },
        {
          name: 'points',
          type: 'number',
          defaultValue: 1,
          admin: {
            description: 'Points awarded for correct answer',
          },
        },
        {
          name: 'codeExample',
          type: 'code',
          admin: {
            description: 'Optional code example for the question',
          },
        },
      ],
    },
    {
      name: 'relatedTutorials',
      type: 'relationship',
      relationTo: 'tutorials',
      hasMany: true,
      admin: {
        description: 'Tutorials related to this quiz',
      },
    },
    {
      name: 'meta',
      label: 'SEO',
      type: 'group',
      fields: [
        OverviewField({
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
          imagePath: 'meta.image',
        }),
        MetaTitleField({
          hasGenerateFn: true,
        }),
        MetaImageField({
          relationTo: 'media',
        }),
        MetaDescriptionField({}),
        PreviewField({
          hasGenerateFn: true,
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
        }),
      ],
    },
    ...slugField(),
  ],
  versions: {
    drafts: {
      autosave: {
        interval: 100,
      },
    },
    maxPerDoc: 50,
  },
}
