import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { authenticatedOrPublished } from '../../access/authenticatedOrPublished'
import { slugField } from '@/fields/slug'

import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields'

export const References: CollectionConfig<'references'> = {
  slug: 'references',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  defaultPopulate: {
    term: true,
    category: true,
    definition: true,
  },
  admin: {
    defaultColumns: ['term', 'category', 'status', 'updatedAt'],
    useAsTitle: 'term',
  },
  fields: [
    {
      name: 'term',
      type: 'text',
      required: true,
      admin: {
        description: 'The keyword, property, or function name',
      },
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'categories',
      required: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'subcategory',
      type: 'text',
      admin: {
        description: 'e.g., "Tags", "Properties", "Methods"',
        position: 'sidebar',
      },
    },
    {
      name: 'definition',
      type: 'textarea',
      required: true,
      admin: {
        description: 'Brief explanation of what this term does',
      },
    },
    {
      name: 'syntax',
      type: 'code',
      admin: {
        description: 'Basic syntax example',
      },
    },
    {
      name: 'parameters',
      type: 'array',
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'type',
          type: 'text',
          admin: {
            description: 'e.g., "string", "number", "boolean"',
          },
        },
        {
          name: 'required',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'description',
          type: 'textarea',
        },
      ],
      admin: {
        description: 'Parameters for functions/methods',
      },
    },
    {
      name: 'examples',
      type: 'array',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'code',
          type: 'code',
          required: true,
        },
        {
          name: 'output',
          type: 'textarea',
          admin: {
            description: 'Expected output or result',
          },
        },
        {
          name: 'explanation',
          type: 'textarea',
          admin: {
            description: 'Brief explanation of the example',
          },
        },
      ],
    },
    {
      name: 'browserSupport',
      type: 'group',
      fields: [
        {
          name: 'chrome',
          type: 'text',
          admin: {
            description: 'Minimum Chrome version',
          },
        },
        {
          name: 'firefox',
          type: 'text',
          admin: {
            description: 'Minimum Firefox version',
          },
        },
        {
          name: 'safari',
          type: 'text',
          admin: {
            description: 'Minimum Safari version',
          },
        },
        {
          name: 'edge',
          type: 'text',
          admin: {
            description: 'Minimum Edge version',
          },
        },
      ],
      admin: {
        description: 'Browser compatibility information',
      },
    },
    {
      name: 'relatedTerms',
      type: 'relationship',
      relationTo: 'references',
      hasMany: true,
      admin: {
        description: 'Related reference items',
      },
    },
    {
      name: 'relatedTutorials',
      type: 'relationship',
      relationTo: 'tutorials',
      hasMany: true,
      admin: {
        description: 'Tutorials that use this reference',
      },
    },
    {
      name: 'meta',
      label: 'SEO & Social Media',
      type: 'group',
      fields: [
        OverviewField({
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
          imagePath: 'meta.image',
        }),
        MetaTitleField({
          hasGenerateFn: true,
        }),
        MetaDescriptionField({}),
        MetaImageField({
          relationTo: 'media',
        }),
        {
          name: 'keywords',
          type: 'text',
          admin: {
            description:
              'SEO keywords separated by commas (e.g., "CSS flexbox, layout, reference")',
          },
        },
        PreviewField({
          hasGenerateFn: true,
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
        }),
      ],
    },
    ...slugField(),
  ],
  versions: {
    drafts: {
      autosave: {
        interval: 100,
      },
    },
    maxPerDoc: 50,
  },
}
