import type { CollectionA<PERSON><PERSON>hangeHook } from 'payload'

import { revalidatePath, revalidateTag } from 'next/cache'

import type { Tutorial } from '../../../payload-types'

export const revalidatePage: CollectionAfterChangeHook<Tutorial> = ({
  doc,
  previousDoc,
  req: { payload, context },
}) => {
  if (!context.disableRevalidate) {
    if (doc._status === 'published') {
      const path = `/tutorials/${doc.slug}`

      payload.logger.info(`Revalidating tutorial at path: ${path}`)

      revalidatePath(path)
      revalidateTag(`tutorial_${doc.slug}`)
    }

    // If the tutorial was previously published, but now is draft, revalidate the old path
    if (previousDoc?._status === 'published' && doc._status !== 'published') {
      const oldPath = `/tutorials/${previousDoc.slug}`

      payload.logger.info(`Revalidating old tutorial path: ${oldPath}`)

      revalidatePath(oldPath)
      revalidateTag(`tutorial_${previousDoc.slug}`)
    }
  }

  return doc
}
