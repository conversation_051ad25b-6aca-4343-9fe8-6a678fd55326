import type { CollectionConfig } from 'payload'

import { authenticatedAdmin } from '../../access/authenticated'

export const Users: CollectionConfig = {
  slug: 'users',
  access: {
    // Only admins can access the admin panel for users
    admin: ({ req: { user } }) => {
      return user?.role === 'admin'
    },
    // Allow public registration
    create: () => true,
    // Only admins can delete users
    delete: ({ req: { user } }) => {
      return user?.role === 'admin'
    },
    // Admins can read all users, users can read their own profile
    read: ({ req: { user } }) => {
      if (user?.role === 'admin') {
        return true
      }
      if (user) {
        return {
          id: {
            equals: user.id,
          },
        }
      }
      return false
    },
    // Admins can update any user, users can update their own profile
    update: ({ req: { user } }) => {
      if (user?.role === 'admin') {
        return true
      }
      if (user) {
        return {
          id: {
            equals: user.id,
          },
        }
      }
      return false
    },
  },
  admin: {
    defaultColumns: ['name', 'email', 'role', 'createdAt'],
    useAsTitle: 'name',
    description: 'Manage user accounts and permissions',
  },
  auth: true,
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      admin: {
        description: 'Full name of the user',
      },
    },
    {
      name: 'role',
      type: 'select',
      required: true,
      defaultValue: 'user',
      options: [
        {
          label: 'User',
          value: 'user',
        },
        {
          label: 'Admin',
          value: 'admin',
        },
      ],
      admin: {
        description: 'User role determines access permissions',
        position: 'sidebar',
        // Only admins can change user roles
        condition: ({ req: { user } }) => {
          return user?.role === 'admin'
        },
      },
      access: {
        // Only admins can update roles
        update: ({ req: { user } }) => {
          return user?.role === 'admin'
        },
      },
    },
    {
      name: 'avatar',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Profile picture',
        position: 'sidebar',
      },
    },
    {
      name: 'bio',
      type: 'textarea',
      admin: {
        description: 'Short bio or description',
      },
    },
    {
      name: 'preferences',
      type: 'group',
      fields: [
        {
          name: 'emailNotifications',
          type: 'checkbox',
          defaultValue: true,
          admin: {
            description: 'Receive email notifications for course updates',
          },
        },
        {
          name: 'theme',
          type: 'select',
          defaultValue: 'system',
          options: [
            { label: 'System', value: 'system' },
            { label: 'Light', value: 'light' },
            { label: 'Dark', value: 'dark' },
          ],
        },
      ],
      admin: {
        description: 'User preferences and settings',
      },
    },
  ],
  hooks: {
    beforeChange: [
      async ({ data, req, operation }) => {
        // For new user creation, check if this should be the first admin
        if (operation === 'create' && !data.role) {
          // Check if this is the first user in the system
          const existingUsers = await req.payload.find({
            collection: 'users',
            limit: 1,
            pagination: false,
          })

          if (existingUsers.totalDocs === 0) {
            // This is the first user - make them admin for better UX
            data.role = 'admin'
            console.log('🎉 First user registered! Automatically assigned admin role.')
          } else {
            // Not the first user - assign default user role
            data.role = 'user'
          }
        }

        // Prevent non-admins from changing their own role
        if (operation === 'update' && req.user?.role !== 'admin') {
          delete data.role
        }

        return data
      },
    ],
  },
  timestamps: true,
}
