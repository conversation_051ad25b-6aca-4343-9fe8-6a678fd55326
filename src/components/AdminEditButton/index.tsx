'use client'

import React from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Edit } from 'lucide-react'

interface AdminEditButtonProps {
  editUrl: string
  className?: string
}

export const AdminEditButton: React.FC<AdminEditButtonProps> = ({ editUrl, className = '' }) => {
  const handleEditClick = (e: React.MouseEvent) => {
    e.preventDefault()

    // Ensure the URL is properly formatted
    const adminUrl = editUrl.startsWith('http') ? editUrl : `${window.location.origin}${editUrl}`

    // Open admin panel in new tab
    window.open(adminUrl, '_blank', 'noopener,noreferrer')
  }

  return (
    <Button
      variant="outline"
      size="sm"
      className={`flex items-center gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 ${className}`}
      onClick={handleEditClick}
    >
      <Edit className="h-4 w-4" />
      Edit Tutorial
    </Button>
  )
}
