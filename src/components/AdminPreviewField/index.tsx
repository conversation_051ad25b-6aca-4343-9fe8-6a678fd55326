'use client'

import React, { useEffect, useState } from 'react'
import { RichTextPreview } from '@/components/RichTextPreview'
import type { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'

interface AdminPreviewFieldProps {
  /** Field name to watch for changes */
  fieldName?: string
  /** Custom title for the preview */
  title?: string
  /** Custom description */
  description?: string
  /** Size of the preview modal */
  size?: 'small' | 'medium' | 'large' | 'fullscreen'
  /** Show word/character counts */
  showStats?: boolean
}

// This component integrates with Payload's form context
export const AdminPreviewField: React.FC<AdminPreviewFieldProps> = ({
  fieldName = 'content',
  title = 'Content Preview',
  description = 'Preview how your content will appear on the frontend',
  size = 'large',
  showStats = true,
}) => {
  const [richTextData, setRichTextData] = useState<DefaultTypedEditorState | null>(null)

  useEffect(() => {
    // Function to extract rich text data from the form
    const extractRichTextData = () => {
      try {
        // Look for Lexical editor instances in the DOM
        const lexicalEditors = document.querySelectorAll('[data-lexical-editor="true"]')

        if (lexicalEditors.length > 0) {
          // Try to get the editor state from the first Lexical editor found
          const editorElement = lexicalEditors[0] as any

          // This is a simplified approach - in a real implementation,
          // you'd want to properly integrate with Lexical's editor state
          if (editorElement && editorElement.__lexicalEditor) {
            const editorState = editorElement.__lexicalEditor.getEditorState()
            if (editorState) {
              setRichTextData(editorState.toJSON())
            }
          }
        }

        // Fallback: try to get data from form inputs
        const formData = new FormData()
        const form = document.querySelector('form')
        if (form) {
          const formDataObj = new FormData(form)
          const contentField = formDataObj.get(fieldName)
          if (contentField && typeof contentField === 'string') {
            try {
              const parsedData = JSON.parse(contentField)
              setRichTextData(parsedData)
            } catch (e) {
              // If parsing fails, create a simple text node
              setRichTextData({
                root: {
                  children: [
                    {
                      type: 'paragraph',
                      children: [
                        {
                          type: 'text',
                          text: contentField,
                          version: 1,
                          mode: 'normal',
                          style: '',
                          format: 0,
                          detail: 0,
                        },
                      ],
                      version: 1,
                      direction: 'ltr',
                      format: '',
                      indent: 0,
                    },
                  ],
                  direction: 'ltr',
                  format: '',
                  indent: 0,
                  type: 'root',
                  version: 1,
                },
              })
            }
          }
        }
      } catch (error) {
        console.warn('Could not extract rich text data:', error)
      }
    }

    // Initial extraction
    extractRichTextData()

    // Set up observers for form changes
    const observer = new MutationObserver(() => {
      extractRichTextData()
    })

    // Observe form changes
    const form = document.querySelector('form')
    if (form) {
      observer.observe(form, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['value'],
      })
    }

    // Also listen for input events
    const handleInputChange = () => {
      setTimeout(extractRichTextData, 100) // Small delay to ensure state is updated
    }

    document.addEventListener('input', handleInputChange)
    document.addEventListener('change', handleInputChange)

    return () => {
      observer.disconnect()
      document.removeEventListener('input', handleInputChange)
      document.removeEventListener('change', handleInputChange)
    }
  }, [fieldName])

  return (
    <div
      style={{
        marginBottom: '16px',
        padding: '12px',
        backgroundColor: '#f8f9fa',
        border: '1px solid #e1e5e9',
        borderRadius: '4px',
      }}
    >
      <div style={{ marginBottom: '8px' }}>
        <h4
          style={{
            margin: '0 0 4px 0',
            fontSize: '14px',
            fontWeight: '600',
            color: '#374151',
          }}
        >
          📖 {title}
        </h4>
        {description && (
          <p
            style={{
              margin: '0',
              fontSize: '12px',
              color: '#6b7280',
            }}
          >
            {description}
          </p>
        )}
      </div>

      <RichTextPreview
        data={richTextData || undefined}
        title={title}
        size={size}
        showWordCount={showStats}
        showCharCount={showStats}
        buttonText="👁️ Preview Content"
      />
    </div>
  )
}

// Enhanced version that can work with specific field paths
export const AdminPreviewFieldAdvanced: React.FC<
  AdminPreviewFieldProps & {
    /** Function to extract data from form context */
    dataExtractor?: () => DefaultTypedEditorState | null
    /** Watch for changes in specific form fields */
    watchFields?: string[]
  }
> = ({
  fieldName = 'content',
  title = 'Content Preview',
  description = 'Preview how your content will appear on the frontend',
  size = 'large',
  showStats = true,
  dataExtractor,
  watchFields = [],
}) => {
  const [richTextData, setRichTextData] = useState<DefaultTypedEditorState | null>(null)

  useEffect(() => {
    const updateData = () => {
      if (dataExtractor) {
        const data = dataExtractor()
        setRichTextData(data)
      }
    }

    // Initial update
    updateData()

    // Set up interval to check for changes
    const interval = setInterval(updateData, 1000)

    return () => clearInterval(interval)
  }, [dataExtractor])

  return (
    <div
      style={{
        marginBottom: '16px',
        padding: '12px',
        backgroundColor: '#f0f9ff',
        border: '1px solid #0ea5e9',
        borderRadius: '4px',
      }}
    >
      <div style={{ marginBottom: '8px' }}>
        <h4
          style={{
            margin: '0 0 4px 0',
            fontSize: '14px',
            fontWeight: '600',
            color: '#0369a1',
          }}
        >
          📖 {title}
        </h4>
        {description && (
          <p
            style={{
              margin: '0',
              fontSize: '12px',
              color: '#0284c7',
            }}
          >
            {description}
          </p>
        )}
      </div>

      <RichTextPreview
        data={richTextData || undefined}
        title={title}
        size={size}
        showWordCount={showStats}
        showCharCount={showStats}
        buttonText="👁️ Preview Content"
      />
    </div>
  )
}
