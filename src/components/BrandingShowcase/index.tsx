import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { brandingConfig } from '@/config/branding'
import { Palette, Type, Layout, Zap } from 'lucide-react'

export const BrandingShowcase: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            GuruDevs Design System
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Complete visual branding guide and component showcase
          </p>
        </div>

        {/* Brand Identity */}
        <section className="mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Brand Identity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-2xl font-bold text-brand-600 mb-2">
                  {brandingConfig.brand.name}
                </h3>
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-2">
                  {brandingConfig.brand.tagline}
                </p>
                <p className="text-gray-600 dark:text-gray-400">
                  {brandingConfig.brand.description}
                </p>
              </div>
              <div className="bg-brand-50 dark:bg-brand-900/20 p-4 rounded-lg">
                <h4 className="font-semibold mb-2">Mission</h4>
                <p className="text-gray-700 dark:text-gray-300">
                  {brandingConfig.brand.mission}
                </p>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Color Palette */}
        <section className="mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Color Palette
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Primary Colors */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">Primary Brand Colors</h3>
                <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
                  {Object.entries(brandingConfig.colors.primary).map(([shade, color]) => (
                    <div key={shade} className="text-center">
                      <div
                        className="w-full h-16 rounded-lg mb-2 border border-gray-200 dark:border-gray-700"
                        style={{ backgroundColor: color }}
                      ></div>
                      <div className="text-xs font-mono text-gray-600 dark:text-gray-400">
                        {shade}
                      </div>
                      <div className="text-xs font-mono text-gray-500 dark:text-gray-500">
                        {color}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Language Colors */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">Programming Language Colors</h3>
                <div className="grid grid-cols-4 md:grid-cols-8 gap-4">
                  {Object.entries(brandingConfig.colors.languages).map(([lang, color]) => (
                    <div key={lang} className="text-center">
                      <div
                        className="w-full h-12 rounded-lg mb-2 border border-gray-200 dark:border-gray-700"
                        style={{ backgroundColor: color }}
                      ></div>
                      <div className="text-xs font-medium capitalize">{lang}</div>
                      <div className="text-xs font-mono text-gray-500">{color}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Difficulty Colors */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Difficulty Level Colors</h3>
                <div className="grid grid-cols-3 gap-4">
                  {Object.entries(brandingConfig.colors.difficulty).map(([level, color]) => (
                    <div key={level} className="text-center">
                      <div
                        className="w-full h-12 rounded-lg mb-2 border border-gray-200 dark:border-gray-700"
                        style={{ backgroundColor: color }}
                      ></div>
                      <div className="text-sm font-medium capitalize">{level}</div>
                      <div className="text-xs font-mono text-gray-500">{color}</div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Typography */}
        <section className="mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Type className="h-5 w-5" />
                Typography
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Font Families</h3>
                  <div className="space-y-3">
                    <div>
                      <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Primary Font</div>
                      <div className="text-lg" style={{ fontFamily: brandingConfig.typography.fonts.primary }}>
                        {brandingConfig.typography.fonts.primary}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Monospace Font</div>
                      <div className="text-lg" style={{ fontFamily: brandingConfig.typography.fonts.mono }}>
                        {brandingConfig.typography.fonts.mono}
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-4">Type Scale</h3>
                  <div className="space-y-3">
                    {Object.entries(brandingConfig.typography.scale).map(([size, value]) => (
                      <div key={size} className="flex items-center gap-4">
                        <div className="w-16 text-sm text-gray-600 dark:text-gray-400">{size}</div>
                        <div className="text-sm font-mono text-gray-500">{value}</div>
                        <div style={{ fontSize: value }}>Sample Text</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Components */}
        <section className="mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layout className="h-5 w-5" />
                Component Examples
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {/* Buttons */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Buttons</h3>
                  <div className="flex flex-wrap gap-4">
                    <Button>Primary Button</Button>
                    <Button variant="outline">Secondary Button</Button>
                    <Button className="bg-difficulty-beginner hover:bg-difficulty-beginner/90">
                      Success Button
                    </Button>
                    <Button className="bg-difficulty-advanced hover:bg-difficulty-advanced/90">
                      Danger Button
                    </Button>
                  </div>
                </div>

                {/* Badges */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Badges</h3>
                  <div className="flex flex-wrap gap-4">
                    <Badge>Default</Badge>
                    <Badge variant="secondary">Secondary</Badge>
                    <Badge className="bg-difficulty-beginner/10 text-difficulty-beginner">
                      Beginner
                    </Badge>
                    <Badge className="bg-difficulty-intermediate/10 text-difficulty-intermediate">
                      Intermediate
                    </Badge>
                    <Badge className="bg-difficulty-advanced/10 text-difficulty-advanced">
                      Advanced
                    </Badge>
                  </div>
                </div>

                {/* Language Badges */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Language Badges</h3>
                  <div className="flex flex-wrap gap-4">
                    {Object.entries(brandingConfig.colors.languages).slice(0, 6).map(([lang, color]) => (
                      <Badge
                        key={lang}
                        style={{
                          backgroundColor: color + '20',
                          color: color,
                          borderColor: color + '40',
                        }}
                        className="border"
                      >
                        {lang.toUpperCase()}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Cards */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Cards</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle>Tutorial Card</CardTitle>
                        <CardDescription>
                          Learn the basics of HTML and create your first web page
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <Badge className="bg-html/10 text-html">HTML</Badge>
                          <Badge className="bg-difficulty-beginner/10 text-difficulty-beginner">
                            Beginner
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-brand-200 dark:border-brand-700">
                      <CardHeader>
                        <CardTitle className="text-brand-600">Featured Tutorial</CardTitle>
                        <CardDescription>
                          Advanced JavaScript concepts and modern ES6+ features
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <Badge className="bg-javascript/10 text-javascript">JavaScript</Badge>
                          <Badge className="bg-difficulty-advanced/10 text-difficulty-advanced">
                            Advanced
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
}
