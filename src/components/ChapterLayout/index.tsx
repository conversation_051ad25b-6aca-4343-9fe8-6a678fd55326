import React from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Clock, BookOpen, CheckCircle, Play, Target, Users } from 'lucide-react'
import RichText from '@/components/RichText'
import { getDifficultyColor } from '@/config/branding'

interface Tutorial {
  id: string
  title: string
  slug: string
  excerpt?: string
  lessonNumber: number
  estimatedTime?: string
  isCompleted?: boolean
}

interface Chapter {
  id: string
  title: string
  description?: string
  overview: any // Rich text content
  category: {
    title: string
    slug: string
    color?: string
  }
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  order: number
  estimatedTime?: string
  icon?: string
  learningObjectives?: Array<{
    objective: string
  }>
  prerequisites?: Array<{
    prerequisite: string
  }>
  previousChapter?: {
    title: string
    slug: string
  }
  nextChapter?: {
    title: string
    slug: string
  }
  tutorials: Tutorial[]
}

interface ChapterLayoutProps {
  chapter: Chapter
}

const difficultyColors = {
  beginner: 'bg-difficulty-beginner/10 text-difficulty-beginner border-difficulty-beginner/20',
  intermediate: 'bg-difficulty-intermediate/10 text-difficulty-intermediate border-difficulty-intermediate/20',
  advanced: 'bg-difficulty-advanced/10 text-difficulty-advanced border-difficulty-advanced/20',
}

export const ChapterLayout: React.FC<ChapterLayoutProps> = ({ chapter }) => {
  const completedLessons = chapter.tutorials.filter(t => t.isCompleted).length
  const totalLessons = chapter.tutorials.length
  const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Back Navigation */}
        <div className="mb-6">
          <Link href={`/${chapter.category.slug}`}>
            <Button variant="ghost" size="sm" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to {chapter.category.title}
            </Button>
          </Link>
          
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <Link href="/" className="hover:text-brand-600">Home</Link>
            <span>/</span>
            <Link href={`/${chapter.category.slug}`} className="hover:text-brand-600">
              {chapter.category.title}
            </Link>
            <span>/</span>
            <span className="text-gray-900 dark:text-gray-100">Chapter {chapter.order}</span>
          </nav>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Chapter Header */}
            <div>
              <div className="flex items-center gap-3 mb-4">
                <Badge 
                  variant="secondary"
                  style={{ 
                    backgroundColor: chapter.category.color + '20', 
                    color: chapter.category.color 
                  }}
                >
                  {chapter.category.title}
                </Badge>
                <Badge className={difficultyColors[chapter.difficulty]}>
                  {chapter.difficulty}
                </Badge>
                <Badge variant="outline">
                  Chapter {chapter.order}
                </Badge>
              </div>
              
              <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {chapter.title}
              </h1>
              
              {chapter.description && (
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                  {chapter.description}
                </p>
              )}

              {/* Progress Bar */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Progress: {completedLessons} of {totalLessons} lessons completed
                  </span>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {Math.round(progressPercentage)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-brand-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
              </div>

              {/* Chapter Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <BookOpen className="h-6 w-6 text-brand-600 mx-auto mb-1" />
                  <div className="text-sm font-medium">{totalLessons} Lessons</div>
                </div>
                {chapter.estimatedTime && (
                  <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Clock className="h-6 w-6 text-green-600 mx-auto mb-1" />
                    <div className="text-sm font-medium">{chapter.estimatedTime}</div>
                  </div>
                )}
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Target className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-sm font-medium">{chapter.difficulty}</div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Users className="h-6 w-6 text-orange-600 mx-auto mb-1" />
                  <div className="text-sm font-medium">All Levels</div>
                </div>
              </div>
            </div>

            {/* Chapter Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Chapter Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose dark:prose-invert max-w-none">
                  <RichText data={chapter.overview} />
                </div>
              </CardContent>
            </Card>

            {/* Learning Objectives */}
            {chapter.learningObjectives && chapter.learningObjectives.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    What You&apos;ll Learn
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {chapter.learningObjectives.map((obj, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 dark:text-gray-300">{obj.objective}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Prerequisites */}
            {chapter.prerequisites && chapter.prerequisites.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Prerequisites</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {chapter.prerequisites.map((prereq, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-700 dark:text-gray-300">{prereq.prerequisite}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar - Lessons */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Chapter Lessons</CardTitle>
                <CardDescription>
                  Complete lessons in order for the best learning experience
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {chapter.tutorials.map((tutorial, index) => (
                    <Link key={tutorial.id} href={`/tutorials/${tutorial.slug}`}>
                      <div className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer">
                        <div className="flex-shrink-0">
                          {tutorial.isCompleted ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : (
                            <Play className="h-5 w-5 text-gray-400" />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              Lesson {tutorial.lessonNumber}
                            </span>
                            {tutorial.estimatedTime && (
                              <span className="text-xs text-gray-400">
                                {tutorial.estimatedTime}
                              </span>
                            )}
                          </div>
                          <h4 className="font-medium text-gray-900 dark:text-white text-sm line-clamp-2">
                            {tutorial.title}
                          </h4>
                          {tutorial.excerpt && (
                            <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2 mt-1">
                              {tutorial.excerpt}
                            </p>
                          )}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Chapter Navigation */}
            <Card>
              <CardHeader>
                <CardTitle>Chapter Navigation</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {chapter.previousChapter && (
                  <Link href={`/chapters/${chapter.previousChapter.slug}`}>
                    <Button variant="outline" className="w-full justify-start">
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Previous: {chapter.previousChapter.title}
                    </Button>
                  </Link>
                )}
                
                {chapter.nextChapter && (
                  <Link href={`/chapters/${chapter.nextChapter.slug}`}>
                    <Button className="w-full justify-start">
                      Next: {chapter.nextChapter.title}
                      <ArrowLeft className="h-4 w-4 ml-2 rotate-180" />
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
                <Button variant="outline" className="w-full">
                  <Target className="h-4 w-4 mr-2" />
                  Take Quiz
                </Button>
                <Button variant="outline" className="w-full">
                  <Users className="h-4 w-4 mr-2" />
                  Join Discussion
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
