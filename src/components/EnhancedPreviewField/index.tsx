'use client'

import React, { useEffect, useState } from 'react'
import { RichTextPreview } from '@/components/RichTextPreview'
import type { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'

interface EnhancedPreviewFieldProps {
  /** Field name to watch for changes */
  fieldName?: string
  /** Custom title for the preview */
  title?: string
  /** Custom description */
  description?: string
  /** Size of the preview modal */
  size?: 'small' | 'medium' | 'large' | 'fullscreen'
  /** Show word/character counts */
  showStats?: boolean
  /** Custom button text */
  buttonText?: string
}

// Convert markdown text to Lexical-compatible structure
const convertMarkdownToLexical = (markdownText: string): DefaultTypedEditorState => {
  const lines = markdownText.split('\n')
  const children: any[] = []

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]?.trim() || ''

    if (!line) {
      // Empty line
      children.push({
        type: 'paragraph',
        children: [{ text: '' }],
      })
      continue
    }

    // Headers
    if (line.startsWith('#')) {
      const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
      if (headerMatch && headerMatch[1] && headerMatch[2]) {
        const level = headerMatch[1].length
        const text = headerMatch[2]
        children.push({
          type: 'heading',
          tag: `h${level}`,
          children: [{ text: parseInlineMarkdown(text) }],
        })
        continue
      }
    }

    // Horizontal rules
    if (line.match(/^---+$/)) {
      children.push({
        type: 'horizontalrule',
      })
      continue
    }

    // Tables (simple detection)
    if (line.includes('|')) {
      children.push({
        type: 'paragraph',
        children: [{ text: line }], // For now, just show as text
      })
      continue
    }

    // Regular paragraph
    children.push({
      type: 'paragraph',
      children: [{ text: parseInlineMarkdown(line) }],
    })
  }

  return {
    root: {
      children,
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  }
}

// Parse inline markdown (bold, italic, code, links)
const parseInlineMarkdown = (text: string): string => {
  // For now, just return the text as-is
  // In a full implementation, you'd parse **bold**, *italic*, `code`, etc.
  return text
}

export const EnhancedPreviewField: React.FC<EnhancedPreviewFieldProps> = ({
  fieldName = 'content',
  title,
  description = 'Preview how your content will appear on the frontend',
  size = 'large',
  showStats = true,
  buttonText = '👁️ Preview Content',
}) => {
  const [richTextData, setRichTextData] = useState<DefaultTypedEditorState | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Determine title based on field name if not provided
  const previewTitle = title || `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} Preview`

  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    const extractRichTextData = () => {
      setIsLoading(true)

      try {
        // Method 1: Try to find Lexical editor instances
        const lexicalEditors = document.querySelectorAll('[data-lexical-editor="true"]')

        for (const editorElement of lexicalEditors) {
          const editor = (editorElement as any).__lexicalEditor
          if (editor) {
            const editorState = editor.getEditorState()
            if (editorState) {
              const jsonState = editorState.toJSON()
              console.log('Extracted Lexical JSON state:', jsonState)

              if (jsonState && jsonState.root && jsonState.root.children.length > 0) {
                // Check if the content is just markdown text in a single paragraph
                const firstChild = jsonState.root.children[0]
                if (
                  firstChild &&
                  firstChild.type === 'paragraph' &&
                  firstChild.children &&
                  firstChild.children.length === 1 &&
                  firstChild.children[0].text &&
                  (firstChild.children[0].text.includes('#') ||
                    firstChild.children[0].text.includes('**') ||
                    firstChild.children[0].text.includes('|'))
                ) {
                  // This looks like markdown text, convert it
                  console.log('Converting markdown text to structured format')
                  const convertedData = convertMarkdownToLexical(firstChild.children[0].text)
                  setRichTextData(convertedData)
                } else {
                  // This is already structured data
                  setRichTextData(jsonState)
                }
                setIsLoading(false)
                return
              }
            }
          }
        }

        // Method 2: Look for contenteditable elements with markdown-like content
        const contentEditableElements = document.querySelectorAll('[contenteditable="true"]')

        for (const element of contentEditableElements) {
          const textContent = element.textContent || (element as HTMLElement).innerText
          if (textContent && textContent.trim().length > 0) {
            console.log('Found contenteditable text:', textContent)

            // Check if this looks like markdown
            if (
              textContent.includes('#') ||
              textContent.includes('**') ||
              textContent.includes('|')
            ) {
              console.log('Converting contenteditable markdown to structured format')
              const convertedData = convertMarkdownToLexical(textContent)
              setRichTextData(convertedData)
              setIsLoading(false)
              return
            }
          }
        }

        // Method 3: Try to find rich text content in form data
        const richTextElements = document.querySelectorAll(
          '[data-field-name*="richText"], [data-field-name*="content"]',
        )

        for (const element of richTextElements) {
          const fieldData = (element as any).value
          if (fieldData) {
            try {
              const parsedData = JSON.parse(fieldData)
              console.log('Parsed form data:', parsedData)
              if (parsedData && parsedData.root) {
                setRichTextData(parsedData)
                setIsLoading(false)
                return
              }
            } catch (e) {
              // Continue to next method
            }
          }
        }

        // Method 4: Create sample data if no content found
        console.log('No content found, creating sample data')
        setRichTextData({
          root: {
            children: [
              {
                type: 'paragraph',
                version: 1,
                direction: 'ltr',
                format: '',
                indent: 0,
                children: [
                  {
                    type: 'text',
                    text: 'No content available for preview. Start typing in the editor above!',
                    version: 1,
                    mode: 'normal',
                    style: '',
                    format: 0,
                    detail: 0,
                  },
                ],
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        })
      } catch (error) {
        console.warn('Could not extract rich text data:', error)
        setRichTextData(null)
      } finally {
        setIsLoading(false)
      }
    }

    // Initial extraction with delay to ensure DOM is ready
    timeoutId = setTimeout(extractRichTextData, 500)

    // Set up observers for changes
    const observer = new MutationObserver(() => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(extractRichTextData, 300)
    })

    // Observe the entire document for changes
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['value', 'data-lexical-editor'],
    })

    // Listen for form events
    const handleFormChange = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(extractRichTextData, 200)
    }

    document.addEventListener('input', handleFormChange)
    document.addEventListener('change', handleFormChange)
    document.addEventListener('keyup', handleFormChange)

    return () => {
      clearTimeout(timeoutId)
      observer.disconnect()
      document.removeEventListener('input', handleFormChange)
      document.removeEventListener('change', handleFormChange)
      document.removeEventListener('keyup', handleFormChange)
    }
  }, [fieldName])

  return (
    <div
      style={{
        marginBottom: '16px',
        padding: '12px',
        backgroundColor: '#f0f9ff',
        border: '1px solid #0ea5e9',
        borderRadius: '6px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      }}
    >
      <div style={{ marginBottom: '8px' }}>
        <h4
          style={{
            margin: '0 0 4px 0',
            fontSize: '14px',
            fontWeight: '600',
            color: '#0369a1',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
          }}
        >
          📖 {previewTitle} (Enhanced)
          {isLoading && (
            <span
              style={{
                fontSize: '12px',
                color: '#6b7280',
                fontWeight: 'normal',
              }}
            >
              (Loading...)
            </span>
          )}
        </h4>
        {description && (
          <p
            style={{
              margin: '0',
              fontSize: '12px',
              color: '#0284c7',
            }}
          >
            {description}
          </p>
        )}
      </div>

      <RichTextPreview
        data={richTextData || undefined}
        title={previewTitle}
        size={size}
        showWordCount={showStats}
        showCharCount={showStats}
        buttonText={buttonText}
        className="enhanced-preview-field"
      />

      <div
        style={{
          marginTop: '8px',
          fontSize: '11px',
          color: '#6b7280',
          fontStyle: 'italic',
        }}
      >
        💡 Enhanced preview with markdown detection and conversion
      </div>
    </div>
  )
}

// Export both named and default
export default EnhancedPreviewField
