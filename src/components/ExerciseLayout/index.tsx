'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Code, Lightbulb, CheckCircle, XCircle, Eye, EyeOff } from 'lucide-react'
import RichText from '@/components/RichText'
import { getDifficultyColor } from '@/config/branding'

interface Exercise {
  id: string
  title: string
  description: string
  instructions: any // Rich text content
  category: {
    title: string
    slug: string
    color?: string
  }
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  type: 'code-completion' | 'fix-code' | 'build-scratch' | 'multiple-choice'
  language: string
  starterCode?: string
  solutionCode: string
  hints?: Array<{
    hint: string
    order: number
  }>
  expectedOutput?: string
  testCases?: Array<{
    input?: string
    expectedOutput: string
    description?: string
  }>
  relatedTutorials?: Array<{
    title: string
    slug: string
    excerpt?: string
  }>
  nextExercises?: Array<{
    title: string
    slug: string
    difficulty: string
  }>
}

interface ExerciseLayoutProps {
  exercise: Exercise
}

export const ExerciseLayout: React.FC<ExerciseLayoutProps> = ({ exercise }) => {
  const [userCode, setUserCode] = useState(exercise.starterCode || '')
  const [showSolution, setShowSolution] = useState(false)
  const [showHints, setShowHints] = useState(false)
  const [currentHint, setCurrentHint] = useState(0)
  const [isCompleted, setIsCompleted] = useState(false)

  const sortedHints = exercise.hints?.sort((a, b) => a.order - b.order) || []

  const checkSolution = () => {
    // Simple check - in a real implementation, you&apos;d want more sophisticated checking
    const userCodeClean = userCode.trim().replace(/\s+/g, ' ')
    const solutionClean = exercise.solutionCode.trim().replace(/\s+/g, ' ')
    
    if (userCodeClean === solutionClean) {
      setIsCompleted(true)
    } else {
      // Could show feedback about what&apos;s wrong
      alert('Not quite right. Keep trying or check the hints!')
    }
  }

  const resetExercise = () => {
    setUserCode(exercise.starterCode || '')
    setShowSolution(false)
    setShowHints(false)
    setCurrentHint(0)
    setIsCompleted(false)
  }

  const getNextHint = () => {
    if (currentHint < sortedHints.length - 1) {
      setCurrentHint(currentHint + 1)
    }
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="mb-6">
          <Link href="/exercises">
            <Button variant="ghost" size="sm" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Exercises
            </Button>
          </Link>
          
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
            <Link href="/" className="hover:text-brand-600">Home</Link>
            <span>/</span>
            <Link href="/exercises" className="hover:text-brand-600">Exercises</Link>
            <span>/</span>
            <Link 
              href={`/exercises/${exercise.category.slug}`} 
              className="hover:text-brand-600"
            >
              {exercise.category.title}
            </Link>
            <span>/</span>
            <span className="text-gray-900 dark:text-gray-100">{exercise.title}</span>
          </nav>

          <div className="flex items-center gap-3 mb-4">
            <Badge 
              variant="secondary"
              style={{ 
                backgroundColor: exercise.category.color + '20', 
                color: exercise.category.color 
              }}
            >
              {exercise.category.title}
            </Badge>
            <Badge style={{ backgroundColor: getDifficultyColor(exercise.difficulty) + '20', color: getDifficultyColor(exercise.difficulty) }}>
              {exercise.difficulty}
            </Badge>
            <Badge variant="outline">
              {exercise.language.toUpperCase()}
            </Badge>
            <Badge variant="outline">
              {exercise.type.replace('-', ' ')}
            </Badge>
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {exercise.title}
          </h1>
          
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
            {exercise.description}
          </p>

          {isCompleted && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="font-semibold text-green-800 dark:text-green-200">
                    Congratulations! 🎉
                  </h3>
                  <p className="text-green-700 dark:text-green-300">
                    You&apos;ve successfully completed this exercise!
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Instructions & Hints */}
          <div className="space-y-6">
            {/* Instructions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  Instructions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose dark:prose-invert max-w-none">
                  <RichText data={exercise.instructions} />
                </div>
              </CardContent>
            </Card>

            {/* Expected Output */}
            {exercise.expectedOutput && (
              <Card>
                <CardHeader>
                  <CardTitle>Expected Output</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                    <pre className="text-sm font-mono">{exercise.expectedOutput}</pre>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Test Cases */}
            {exercise.testCases && exercise.testCases.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Test Cases</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {exercise.testCases.map((testCase, index) => (
                      <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <h4 className="font-semibold mb-2">Test Case {index + 1}</h4>
                        {testCase.description && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            {testCase.description}
                          </p>
                        )}
                        {testCase.input && (
                          <div className="mb-2">
                            <span className="text-sm font-medium">Input:</span>
                            <div className="bg-gray-100 dark:bg-gray-800 rounded p-2 mt-1">
                              <code className="text-sm">{testCase.input}</code>
                            </div>
                          </div>
                        )}
                        <div>
                          <span className="text-sm font-medium">Expected Output:</span>
                          <div className="bg-gray-100 dark:bg-gray-800 rounded p-2 mt-1">
                            <code className="text-sm">{testCase.expectedOutput}</code>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Hints */}
            {sortedHints.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Lightbulb className="h-5 w-5" />
                      Hints
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowHints(!showHints)}
                    >
                      {showHints ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      {showHints ? 'Hide' : 'Show'} Hints
                    </Button>
                  </CardTitle>
                </CardHeader>
                {showHints && (
                  <CardContent>
                    <div className="space-y-3">
                      {sortedHints.slice(0, currentHint + 1).map((hint, index) => (
                        <div key={index} className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                          <p className="text-sm">
                            <span className="font-medium">Hint {index + 1}:</span> {hint.hint}
                          </p>
                        </div>
                      ))}
                      {currentHint < sortedHints.length - 1 && (
                        <Button variant="outline" size="sm" onClick={getNextHint}>
                          Get Next Hint
                        </Button>
                      )}
                    </div>
                  </CardContent>
                )}
              </Card>
            )}
          </div>

          {/* Right Column - Code Editor */}
          <div className="space-y-6">
            {/* Code Editor */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Your Code</span>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={resetExercise}>
                      Reset
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => setShowSolution(!showSolution)}
                    >
                      {showSolution ? 'Hide' : 'Show'} Solution
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-gray-900 rounded-lg overflow-hidden">
                    <div className="bg-gray-800 px-4 py-2 text-sm text-gray-300 border-b border-gray-700">
                      {exercise.language} - {exercise.type.replace('-', ' ')}
                    </div>
                    <textarea
                      value={userCode}
                      onChange={(e) => setUserCode(e.target.value)}
                      className="w-full h-64 p-4 bg-gray-900 text-green-400 font-mono text-sm resize-none border-none outline-none"
                      placeholder="Write your code here..."
                    />
                  </div>
                  
                  <div className="flex gap-3">
                    <Button onClick={checkSolution} disabled={isCompleted}>
                      {isCompleted ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Completed
                        </>
                      ) : (
                        'Check Solution'
                      )}
                    </Button>
                    <Button variant="outline">
                      Run Code
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Solution */}
            {showSolution && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-yellow-600">Solution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-900 rounded-lg overflow-hidden">
                    <div className="bg-gray-800 px-4 py-2 text-sm text-gray-300 border-b border-gray-700">
                      Solution Code
                    </div>
                    <pre className="p-4 text-green-400 font-mono text-sm overflow-x-auto">
                      {exercise.solutionCode}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Related Content */}
        {(exercise.relatedTutorials?.length || exercise.nextExercises?.length) && (
          <div className="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Related Tutorials */}
            {exercise.relatedTutorials && exercise.relatedTutorials.length > 0 && (
              <section>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                  Related Tutorials
                </h2>
                <div className="space-y-3">
                  {exercise.relatedTutorials.map((tutorial, index) => (
                    <Link key={index} href={`/tutorials/${tutorial.slug}`}>
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-4">
                          <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                            {tutorial.title}
                          </h3>
                          {tutorial.excerpt && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                              {tutorial.excerpt}
                            </p>
                          )}
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </section>
            )}

            {/* Next Exercises */}
            {exercise.nextExercises && exercise.nextExercises.length > 0 && (
              <section>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                  Next Exercises
                </h2>
                <div className="space-y-3">
                  {exercise.nextExercises.map((nextExercise, index) => (
                    <Link key={index} href={`/exercises/${nextExercise.slug}`}>
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <h3 className="font-semibold text-gray-900 dark:text-white">
                              {nextExercise.title}
                            </h3>
                            <Badge 
                              style={{ 
                                backgroundColor: getDifficultyColor(nextExercise.difficulty as any) + '20', 
                                color: getDifficultyColor(nextExercise.difficulty as any) 
                              }}
                            >
                              {nextExercise.difficulty}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>
              </section>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
