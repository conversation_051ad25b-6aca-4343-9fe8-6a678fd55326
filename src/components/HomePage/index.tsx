import React from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowRight, BookOpen, Code, Trophy, Users, Zap, Target, Award } from 'lucide-react'
import { brandingConfig } from '@/config/branding'

interface Category {
  id: string
  title: string
  description: string
  slug: string
  color?: string
  icon?: any
  featured: boolean
}

interface Tutorial {
  id: string
  title: string
  excerpt: string
  slug: string
  category: Category
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime?: string
}

interface HomePageProps {
  featuredCategories: Category[]
  featuredTutorials: Tutorial[]
  stats?: {
    totalTutorials: number
    totalUsers: number
    totalQuizzes: number
  }
}

const difficultyColors = {
  beginner: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300',
  intermediate:
    'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-300',
  advanced: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-300',
}

export const HomePage: React.FC<HomePageProps> = ({
  featuredCategories,
  featuredTutorials,
  stats,
}) => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 dark:from-gray-900 dark:via-gray-800 dark:to-blue-900 py-24 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500 rounded-full blur-xl"></div>
          <div className="absolute top-40 right-20 w-32 h-32 bg-yellow-400 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 left-1/4 w-24 h-24 bg-blue-600 rounded-full blur-xl"></div>
          <div className="absolute bottom-40 right-1/3 w-16 h-16 bg-blue-400 rounded-full blur-xl"></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <div className="text-center max-w-5xl mx-auto">
            {/* Brand Badge */}
            <div className="inline-flex items-center gap-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full px-4 py-2 mb-6 border border-blue-200 dark:border-blue-700">
              <Zap className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                {brandingConfig.brand.tagline}
              </span>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
              Learn to Code with{' '}
              <span className="bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                GuruDevs
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-10 leading-relaxed max-w-3xl mx-auto">
              {brandingConfig.brand.description}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button
                size="lg"
                className="text-lg px-8 py-4 bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all"
                asChild
              >
                <Link href="/tutorials">
                  <Target className="mr-2 h-5 w-5" />
                  Start Learning
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4 border-2 border-blue-200 hover:border-blue-300 hover:bg-blue-50 dark:border-blue-700 dark:hover:border-blue-600 dark:hover:bg-blue-900/50"
                asChild
              >
                <Link href="/tutorials">
                  <BookOpen className="mr-2 h-5 w-5" />
                  Browse Tutorials
                </Link>
              </Button>
            </div>

            {/* Feature Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="flex items-center justify-center gap-3 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-white/20 dark:border-gray-700/20">
                <Code className="h-6 w-6 text-blue-600" />
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  Interactive Coding
                </span>
              </div>
              <div className="flex items-center justify-center gap-3 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-white/20 dark:border-gray-700/20">
                <Award className="h-6 w-6 text-blue-600" />
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  Step-by-Step Learning
                </span>
              </div>
              <div className="flex items-center justify-center gap-3 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 border border-white/20 dark:border-gray-700/20">
                <Trophy className="h-6 w-6 text-blue-600" />
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  Practice & Quizzes
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      {stats && (
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                    <BookOpen className="h-12 w-12 text-blue-600" />
                  </div>
                </div>
                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  {stats.totalTutorials}+
                </h3>
                <p className="text-gray-600 dark:text-gray-300">Interactive Tutorials</p>
              </div>
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                    <Trophy className="h-12 w-12 text-green-600" />
                  </div>
                </div>
                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  {stats.totalQuizzes}+
                </h3>
                <p className="text-gray-600 dark:text-gray-300">Practice Quizzes</p>
              </div>
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                    <Users className="h-12 w-12 text-purple-600" />
                  </div>
                </div>
                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  {stats.totalUsers}+
                </h3>
                <p className="text-gray-600 dark:text-gray-300">Active Learners</p>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Featured Categories */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Popular Programming Languages
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Choose from our most popular programming languages and start building amazing projects
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredCategories.map((category) => (
              <Link key={category.id} href={`/courses/${category.slug}`}>
                <Card
                  className="h-full hover:shadow-xl transition-all duration-300 cursor-pointer group border-2 border-transparent hover:border-opacity-20 hover:-translate-y-2"
                  style={
                    {
                      '--hover-border-color': category.color || brandingConfig.colors.primary[500],
                    } as any
                  }
                >
                  <CardHeader className="text-center">
                    <div
                      className="w-20 h-20 mx-auto rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg"
                      style={{
                        backgroundColor: category.color || brandingConfig.colors.primary[500],
                        boxShadow: `0 10px 25px ${category.color || brandingConfig.colors.primary[500]}20`,
                      }}
                    >
                      <Code className="h-10 w-10 text-white" />
                    </div>
                    <CardTitle className="text-xl font-bold group-hover:text-blue-600 transition-colors">
                      {category.title}
                    </CardTitle>
                    <CardDescription className="text-gray-600 dark:text-gray-400">
                      {category.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button
                      variant="outline"
                      className="w-full group-hover:bg-blue-600 group-hover:text-white group-hover:border-blue-600 transition-all duration-300 font-medium"
                    >
                      Start Learning
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Tutorials */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Featured Tutorials
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Start with these hand-picked tutorials designed for beginners and experts alike
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredTutorials.map((tutorial) => (
              <Link key={tutorial.id} href={`/tutorials/${tutorial.slug}`}>
                <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <Badge
                        variant="secondary"
                        style={{
                          backgroundColor: tutorial.category.color + '20',
                          color: tutorial.category.color,
                        }}
                      >
                        {tutorial.category.title}
                      </Badge>
                      <Badge className={difficultyColors[tutorial.difficulty]}>
                        {tutorial.difficulty}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg line-clamp-2">{tutorial.title}</CardTitle>
                    <CardDescription className="line-clamp-3">{tutorial.excerpt}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      {tutorial.estimatedTime && (
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {tutorial.estimatedTime}
                        </span>
                      )}
                      <ArrowRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button variant="outline" size="lg" asChild>
              <Link href="/tutorials">
                View All Tutorials
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Try It Yourself Section */}
      <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-800 dark:to-gray-700">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Try It Yourself
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Every tutorial includes interactive code editors where you can practice and experiment
              with the code in real-time. No setup required!
            </p>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
              <div className="bg-gray-900 rounded-lg p-4 text-left">
                <div className="flex items-center mb-4">
                  <div className="flex space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <span className="ml-4 text-gray-400 text-sm">index.html</span>
                </div>
                <pre className="text-green-400 text-sm">
                  {`<!DOCTYPE html>
<html>
<head>
    <title>My First Web Page</title>
</head>
<body>
    <h1>Hello, World!</h1>
    <p>Welcome to GuruDevs!</p>
</body>
</html>`}
                </pre>
              </div>
              <Button className="mt-4" asChild>
                <Link href="/tutorials">
                  Try This Example
                  <Code className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
