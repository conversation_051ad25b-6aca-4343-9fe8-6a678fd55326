import clsx from 'clsx'
import React from 'react'

interface Props {
  className?: string
  loading?: 'lazy' | 'eager'
  priority?: 'auto' | 'high' | 'low'
}

export const Logo = (props: Props) => {
  const { loading: loadingFromProps, priority: priorityFromProps, className } = props

  const loading = loadingFromProps || 'lazy'
  const priority = priorityFromProps || 'low'

  return (
    <div className="relative">
      {/* Light theme logo */}
      <img
        alt="GuruDevs Logo"
        width={193}
        height={34}
        loading={loading}
        fetchPriority={priority}
        decoding="async"
        className={clsx('max-w-[9.375rem] w-full h-[34px] dark:hidden', className)}
        src="/gurudevs_lite_icon.png"
      />
      {/* Dark theme logo */}
      <img
        alt="GuruDevs Logo"
        width={193}
        height={34}
        loading={loading}
        fetchPriority={priority}
        decoding="async"
        className={clsx('max-w-[9.375rem] w-full h-[34px] hidden dark:block', className)}
        src="/gurudevs_dark_icon.png"
      />
    </div>
  )
}
