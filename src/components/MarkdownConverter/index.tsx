'use client'

import React, { useState } from 'react'
import { Button } from '@payloadcms/ui'

interface MarkdownConverterProps {
  onConvert?: (lexicalState: any) => void
}

// Convert markdown to Lexical editor state
const convertMarkdownToLexical = (markdown: string) => {
  const lines = markdown.split('\n')
  const nodes: any[] = []

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i] || ''

    if (!line.trim()) {
      // Empty line - add paragraph
      nodes.push({
        type: 'paragraph',
        children: [{ text: '' }],
      })
      continue
    }

    // Headers
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
    if (headerMatch && headerMatch[1] && headerMatch[2]) {
      const level = headerMatch[1].length
      const text = headerMatch[2]
      nodes.push({
        type: 'heading',
        tag: `h${level}`,
        children: [{ text: parseInlineMarkdown(text) }],
      })
      continue
    }

    // Lists
    const listMatch = line.match(/^\s*([-*+]|\d+\.)\s+(.+)$/)
    if (listMatch) {
      const isOrdered = /^\s*\d+\./.test(line)
      const text = listMatch[2]
      nodes.push({
        type: 'listitem',
        listType: isOrdered ? 'number' : 'bullet',
        children: [{ text: parseInlineMarkdown(text || '') }],
      })
      continue
    }

    // Blockquotes
    const quoteMatch = line.match(/^\s*>\s+(.+)$/)
    if (quoteMatch) {
      const text = quoteMatch[1]
      nodes.push({
        type: 'quote',
        children: [{ text: parseInlineMarkdown(text || '') }],
      })
      continue
    }

    // Code blocks
    if (line.trim() === '```' || line.match(/^```\w*$/)) {
      const codeLines = []
      i++ // Skip opening ```
      while (i < lines.length && lines[i]?.trim() !== '```') {
        codeLines.push(lines[i] || '')
        i++
      }
      nodes.push({
        type: 'code',
        language: line.match(/^```(\w+)$/)?.[1] || 'text',
        children: [{ text: codeLines.join('\n') }],
      })
      continue
    }

    // Horizontal rule
    if (line.match(/^---+$/)) {
      nodes.push({
        type: 'horizontalrule',
      })
      continue
    }

    // Regular paragraph
    nodes.push({
      type: 'paragraph',
      children: [{ text: parseInlineMarkdown(line) }],
    })
  }

  return {
    root: {
      children: nodes,
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  }
}

// Parse inline markdown (bold, italic, code, links)
const parseInlineMarkdown = (text: string): string => {
  // This is a simplified version - in production you'd want more sophisticated parsing
  return text
    .replace(/\*\*([^*]+)\*\*/g, '$1') // Remove bold markers for now
    .replace(/\*([^*]+)\*/g, '$1') // Remove italic markers for now
    .replace(/`([^`]+)`/g, '$1') // Remove code markers for now
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove link markers, keep text
}

export const MarkdownConverter: React.FC<MarkdownConverterProps> = ({ onConvert }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [markdown, setMarkdown] = useState('')
  const [preview, setPreview] = useState('')

  const handleConvert = () => {
    if (markdown.trim()) {
      const lexicalState = convertMarkdownToLexical(markdown)

      if (onConvert) {
        onConvert(lexicalState)
        alert('✅ Markdown converted and inserted into editor!')
      } else {
        // Fallback: show preview
        setPreview(JSON.stringify(lexicalState, null, 2))
        alert('✅ Markdown converted! See preview below.')
      }
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && (file.type === 'text/markdown' || file.name.endsWith('.md'))) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        setMarkdown(content)
      }
      reader.readAsText(file)
    }
  }

  if (!isOpen) {
    return (
      <Button onClick={() => setIsOpen(true)} size="small" buttonStyle="secondary">
        🔄 Convert Markdown
      </Button>
    )
  }

  return (
    <div
      style={{
        border: '1px solid #e1e5e9',
        borderRadius: '4px',
        padding: '16px',
        marginBottom: '16px',
        backgroundColor: '#f0f9ff',
      }}
    >
      <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: '600' }}>
        🔄 Convert Markdown to Rich Text
      </h4>
      <p style={{ margin: '0 0 12px 0', fontSize: '12px', color: '#6b7280' }}>
        Convert Markdown directly to rich text format and insert into the editor.
      </p>

      <div style={{ marginBottom: '12px' }}>
        <label
          style={{ display: 'block', marginBottom: '4px', fontSize: '12px', fontWeight: '500' }}
        >
          Upload .md file:
        </label>
        <input
          type="file"
          accept=".md,.markdown"
          onChange={handleFileUpload}
          style={{ fontSize: '12px' }}
        />
      </div>

      <div style={{ marginBottom: '12px' }}>
        <label
          style={{ display: 'block', marginBottom: '4px', fontSize: '12px', fontWeight: '500' }}
        >
          Or paste Markdown text:
        </label>
        <textarea
          value={markdown}
          onChange={(e) => setMarkdown(e.target.value)}
          placeholder="Paste your Markdown content here..."
          style={{
            width: '100%',
            height: '200px',
            padding: '8px',
            border: '1px solid #d1d5db',
            borderRadius: '4px',
            fontSize: '12px',
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
            resize: 'vertical',
          }}
        />
      </div>

      <div style={{ display: 'flex', gap: '8px', marginBottom: '12px' }}>
        <Button onClick={handleConvert} size="small" disabled={!markdown.trim()}>
          🔄 Convert & Insert
        </Button>
        <Button
          onClick={() => {
            setIsOpen(false)
            setMarkdown('')
            setPreview('')
          }}
          size="small"
          buttonStyle="secondary"
        >
          Cancel
        </Button>
      </div>

      {preview && (
        <div style={{ marginTop: '12px' }}>
          <label
            style={{ display: 'block', marginBottom: '4px', fontSize: '12px', fontWeight: '500' }}
          >
            Converted Lexical State (Preview):
          </label>
          <pre
            style={{
              backgroundColor: '#f3f4f6',
              padding: '8px',
              borderRadius: '4px',
              fontSize: '10px',
              overflow: 'auto',
              maxHeight: '200px',
            }}
          >
            {preview}
          </pre>
        </div>
      )}

      <div style={{ marginTop: '12px', fontSize: '11px', color: '#6b7280' }}>
        <strong>How it works:</strong>
        <ol style={{ margin: '4px 0', paddingLeft: '16px' }}>
          <li>Upload .md file or paste Markdown text above</li>
          <li>Click &quot;🔄 Convert &amp; Insert&quot;</li>
          <li>Markdown is converted to rich text format</li>
          <li>Content is inserted directly into the editor</li>
        </ol>
      </div>
    </div>
  )
}
