'use client'

import React, { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Upload, FileText, AlertCircle, CheckCircle } from 'lucide-react'

export const MarkdownImportField: React.FC = () => {
  const [markdownText, setMarkdownText] = useState('')
  const [importStatus, setImportStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type === 'text/markdown') {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        setMarkdownText(content)
      }
      reader.readAsText(file)
    }
  }

  const copyToClipboard = async () => {
    if (!markdownText.trim()) {
      setErrorMessage('Please enter some markdown content')
      setImportStatus('error')
      return
    }

    try {
      await navigator.clipboard.writeText(markdownText)
      setImportStatus('success')

      // Clear success message after 3 seconds
      setTimeout(() => setImportStatus('idle'), 3000)
    } catch (error) {
      console.error('Error copying to clipboard:', error)
      setErrorMessage('Failed to copy to clipboard.')
      setImportStatus('error')
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Import Markdown
          <Badge variant="outline">Beta</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-gray-600">
          Import markdown content to automatically populate the rich text editor. Supports standard
          markdown syntax and will convert to the appropriate rich text blocks.
        </div>

        {/* File Upload */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Upload Markdown File</label>
          <div className="flex gap-2">
            <input
              ref={fileInputRef}
              type="file"
              accept=".md,.markdown"
              onChange={handleFileUpload}
              className="hidden"
            />
            <Button type="button" variant="outline" onClick={() => fileInputRef.current?.click()}>
              <Upload className="h-4 w-4 mr-2" />
              Choose File
            </Button>
          </div>
        </div>

        {/* Text Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Or Paste Markdown</label>
          <textarea
            placeholder="# Your Markdown Content

Paste your markdown content here...

## Features Supported:
- **Bold** and *italic* text
- Headers (H1-H6)
- Lists and links
- Code blocks
- Blockquotes
- And more!"
            value={markdownText}
            onChange={(e) => setMarkdownText(e.target.value)}
            rows={10}
            className="w-full p-3 border border-gray-300 rounded-lg font-mono text-sm resize-vertical focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Status Messages */}
        {importStatus === 'success' && (
          <div className="flex items-center gap-2 text-green-600 bg-green-50 p-3 rounded-lg">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm">
              Markdown copied to clipboard! You can now paste it into the rich text editor.
            </span>
          </div>
        )}

        {importStatus === 'error' && (
          <div className="flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-lg">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm">{errorMessage}</span>
          </div>
        )}

        {/* Import Button */}
        <div className="flex gap-2">
          <Button type="button" onClick={copyToClipboard} disabled={!markdownText.trim()}>
            <FileText className="h-4 w-4 mr-2" />
            Copy to Clipboard
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              setMarkdownText('')
              setImportStatus('idle')
              setErrorMessage('')
            }}
          >
            Clear
          </Button>
        </div>

        <div className="text-xs text-gray-500 space-y-1">
          <p>
            💡 <strong>Tip:</strong> Copy markdown to clipboard and paste into the rich text editor
            using Ctrl+V (Cmd+V on Mac).
          </p>
          <p>
            🔄 <strong>Note:</strong> The rich text editor supports markdown shortcuts for quick
            formatting.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
