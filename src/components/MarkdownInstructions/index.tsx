'use client'

import React, { useState } from 'react'
import { Button } from '@payloadcms/ui'

export const MarkdownInstructions: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        size="small"
        buttonStyle="secondary"
      >
        📝 Markdown Help
      </Button>
    )
  }

  return (
    <div style={{ 
      border: '1px solid #e1e5e9', 
      borderRadius: '4px', 
      padding: '16px', 
      marginBottom: '16px',
      backgroundColor: '#f0f9ff'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
        <h4 style={{ margin: 0, fontSize: '14px', fontWeight: '600', color: '#0369a1' }}>
          📝 Markdown Support Available!
        </h4>
        <Button
          onClick={() => setIsOpen(false)}
          size="small"
          buttonStyle="secondary"
        >
          ✕
        </Button>
      </div>
      
      <div style={{ fontSize: '12px', color: '#374151', lineHeight: '1.5' }}>
        <p style={{ margin: '0 0 8px 0', fontWeight: '500' }}>
          🚀 Type Markdown directly in the editor below - it converts automatically!
        </p>
        
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px', marginBottom: '12px' }}>
          <div>
            <strong>Type this:</strong>
            <div style={{ fontFamily: 'Monaco, Consolas, monospace', fontSize: '11px', backgroundColor: '#f3f4f6', padding: '8px', borderRadius: '4px', marginTop: '4px' }}>
              <div>## My Heading</div>
              <div>**Bold text**</div>
              <div>*Italic text*</div>
              <div>`inline code`</div>
              <div>- List item</div>
              <div>&gt; Quote</div>
              <div>---</div>
            </div>
          </div>
          
          <div>
            <strong>Get this:</strong>
            <div style={{ backgroundColor: '#f3f4f6', padding: '8px', borderRadius: '4px', marginTop: '4px' }}>
              <h3 style={{ margin: '0 0 4px 0', fontSize: '14px' }}>My Heading</h3>
              <div><strong>Bold text</strong></div>
              <div><em>Italic text</em></div>
              <div><code style={{ backgroundColor: '#e5e7eb', padding: '2px 4px', borderRadius: '2px' }}>inline code</code></div>
              <div>• List item</div>
              <blockquote style={{ margin: '4px 0', paddingLeft: '8px', borderLeft: '3px solid #d1d5db', fontStyle: 'italic' }}>Quote</blockquote>
              <hr style={{ margin: '4px 0', border: 'none', borderTop: '1px solid #d1d5db' }} />
            </div>
          </div>
        </div>

        <div style={{ backgroundColor: '#fef3c7', padding: '8px', borderRadius: '4px', border: '1px solid #f59e0b' }}>
          <strong style={{ color: '#92400e' }}>💡 Pro Tip:</strong>
          <span style={{ color: '#92400e' }}> Type your Markdown syntax and press <strong>Space</strong> or <strong>Enter</strong> to auto-convert!</span>
        </div>

        <div style={{ marginTop: '12px' }}>
          <strong>More shortcuts:</strong>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '4px', marginTop: '4px', fontSize: '11px' }}>
            <div><code># </code> → H1</div>
            <div><code>## </code> → H2</div>
            <div><code>### </code> → H3</div>
            <div><code>1. </code> → Numbered list</div>
            <div><code>- </code> → Bullet list</div>
            <div><code>&gt; </code> → Blockquote</div>
          </div>
        </div>
      </div>
    </div>
  )
}
