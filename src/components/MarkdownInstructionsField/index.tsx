'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { BookOpen, ChevronDown, ChevronRight, Code, FileText, Zap } from 'lucide-react'

export const MarkdownInstructionsField: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950 dark:border-blue-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100">
            <BookOpen className="h-5 w-5" />
            Enhanced Tutorial Editor
            <Badge variant="secondary">With Markdown Support</Badge>
          </CardTitle>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-blue-700 hover:text-blue-900 dark:text-blue-300"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
            {isExpanded ? 'Hide' : 'Show'} Guide
          </Button>
        </div>
      </CardHeader>
      {isExpanded && (
        <CardContent className="space-y-6 text-blue-900 dark:text-blue-100">
          {/* Quick Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-start gap-3 p-3 bg-white dark:bg-blue-900 rounded-lg">
              <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Live Markdown</h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Auto-converts to markdown as you type
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-white dark:bg-blue-900 rounded-lg">
              <FileText className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Import/Export</h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Import markdown files or export content
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-white dark:bg-blue-900 rounded-lg">
              <Code className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Custom Blocks</h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Code blocks, callouts, and more
                </p>
              </div>
            </div>
          </div>

          {/* Markdown Shortcuts */}
          <div className="space-y-3">
            <h3 className="font-semibold flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Markdown Shortcuts (Type these in the editor)
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 bg-white dark:bg-blue-900 rounded">
                  <code className="text-blue-600"># Heading 1</code>
                  <span className="text-blue-700 dark:text-blue-300">Large heading</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white dark:bg-blue-900 rounded">
                  <code className="text-blue-600">## Heading 2</code>
                  <span className="text-blue-700 dark:text-blue-300">Medium heading</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white dark:bg-blue-900 rounded">
                  <code className="text-blue-600">**bold text**</code>
                  <span className="text-blue-700 dark:text-blue-300">Bold formatting</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white dark:bg-blue-900 rounded">
                  <code className="text-blue-600">*italic text*</code>
                  <span className="text-blue-700 dark:text-blue-300">Italic formatting</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center p-2 bg-white dark:bg-blue-900 rounded">
                  <code className="text-blue-600">`inline code`</code>
                  <span className="text-blue-700 dark:text-blue-300">Inline code</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white dark:bg-blue-900 rounded">
                  <code className="text-blue-600">- List item</code>
                  <span className="text-blue-700 dark:text-blue-300">Bullet list</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white dark:bg-blue-900 rounded">
                  <code className="text-blue-600">1. Numbered</code>
                  <span className="text-blue-700 dark:text-blue-300">Numbered list</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white dark:bg-blue-900 rounded">
                  <code className="text-blue-600">&gt; Quote</code>
                  <span className="text-blue-700 dark:text-blue-300">Blockquote</span>
                </div>
              </div>
            </div>
          </div>

          {/* Custom Blocks */}
          <div className="space-y-3">
            <h3 className="font-semibold flex items-center gap-2">
              <Code className="h-4 w-4" />
              Custom Tutorial Blocks
            </h3>
            <div className="space-y-2 text-sm">
              <div className="p-3 bg-white dark:bg-blue-900 rounded-lg">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">Code Block</h4>
                <p className="text-blue-700 dark:text-blue-300 mb-2">
                  Syntax-highlighted code with filename and line highlighting
                </p>
                <code className="text-xs text-blue-600">
                  Use the + button in the editor to add code blocks
                </code>
              </div>
              <div className="p-3 bg-white dark:bg-blue-900 rounded-lg">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">Callout</h4>
                <p className="text-blue-700 dark:text-blue-300 mb-2">
                  Info, warning, error, success, tip, or note callouts
                </p>
                <code className="text-xs text-blue-600">
                  Perfect for highlighting important information
                </code>
              </div>
              <div className="p-3 bg-white dark:bg-blue-900 rounded-lg">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                  Image Comparison
                </h4>
                <p className="text-blue-700 dark:text-blue-300 mb-2">
                  Before/after image comparisons
                </p>
                <code className="text-xs text-blue-600">
                  Great for showing code results or design changes
                </code>
              </div>
            </div>
          </div>

          {/* Workflow */}
          <div className="space-y-3">
            <h3 className="font-semibold flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Recommended Workflow
            </h3>
            <ol className="space-y-2 text-sm list-decimal list-inside text-blue-700 dark:text-blue-300">
              <li>Import existing markdown (if you have it) using the import tool above</li>
              <li>
                Write your tutorial content using the rich text editor with markdown shortcuts
              </li>
              <li>Add custom blocks (code examples, callouts) using the + button</li>
              <li>Preview the auto-generated markdown using the preview tool below</li>
              <li>Copy or download the markdown for use in other systems</li>
            </ol>
          </div>

          <div className="p-3 bg-white dark:bg-blue-900 rounded-lg border border-blue-200 dark:border-blue-700">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              💡 <strong>Pro Tip:</strong> The markdown is automatically generated from your rich
              text content and includes all custom blocks as MDX components. This makes it perfect
              for documentation systems that support MDX.
            </p>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
