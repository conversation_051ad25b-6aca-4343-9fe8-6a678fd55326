'use client'

import React, { useEffect } from 'react'

interface MarkdownPasteHandlerProps {
  onMarkdownPaste?: (markdown: string) => void
}

// Simple markdown detection patterns
const MARKDOWN_PATTERNS = [
  /^#{1,6}\s+.+$/m, // Headers
  /\*\*[^*]+\*\*/g, // Bold
  /\*[^*]+\*/g, // Italic
  /`[^`]+`/g, // Inline code
  /```[\s\S]*?```/g, // Code blocks
  /^\s*[-*+]\s+/m, // Unordered lists
  /^\s*\d+\.\s+/m, // Ordered lists
  /^\s*>\s+/m, // Blockquotes
  /\[([^\]]+)\]\(([^)]+)\)/g, // Links
  /^---+$/m, // Horizontal rules
]

const detectMarkdown = (text: string): boolean => {
  // Check if text contains multiple markdown patterns
  let patternCount = 0
  for (const pattern of MARKDOWN_PATTERNS) {
    if (pattern.test(text)) {
      patternCount++
    }
  }
  
  // Consider it markdown if it has 2+ patterns or specific strong indicators
  return patternCount >= 2 || 
         /^#{1,6}\s+.+$/m.test(text) || // Headers are strong indicators
         /```[\s\S]*?```/g.test(text) || // Code blocks are strong indicators
         (text.includes('**') && text.includes('*')) // Bold and italic together
}

export const MarkdownPasteHandler: React.FC<MarkdownPasteHandlerProps> = ({ onMarkdownPaste }) => {
  useEffect(() => {
    const handlePaste = (event: ClipboardEvent) => {
      const pastedText = event.clipboardData?.getData('text/plain')
      
      if (pastedText && detectMarkdown(pastedText)) {
        // Ask user if they want to convert the markdown
        const shouldConvert = window.confirm(
          '🔍 Markdown detected in clipboard!\n\n' +
          'Would you like to paste it as formatted text using markdown shortcuts?\n\n' +
          'Click "OK" to paste with markdown conversion, or "Cancel" to paste as plain text.'
        )
        
        if (shouldConvert) {
          event.preventDefault()
          if (onMarkdownPaste) {
            onMarkdownPaste(pastedText)
          } else {
            // Fallback: simulate typing with markdown shortcuts
            simulateMarkdownTyping(pastedText)
          }
        }
      }
    }

    // Add paste event listener to document
    document.addEventListener('paste', handlePaste)
    
    return () => {
      document.removeEventListener('paste', handlePaste)
    }
  }, [onMarkdownPaste])

  const simulateMarkdownTyping = (markdown: string) => {
    // This is a simplified approach - in a real implementation,
    // you'd want to integrate more deeply with the Lexical editor
    console.log('Simulating markdown typing:', markdown)
    
    // For now, just show instructions
    alert(
      '📝 Markdown detected!\n\n' +
      'The text has been identified as markdown. Please:\n' +
      '1. Paste the content into the editor\n' +
      '2. Use the built-in markdown shortcuts as you type\n' +
      '3. Press Space or Enter after markdown syntax to convert'
    )
  }

  // This component doesn't render anything visible
  return null
}
