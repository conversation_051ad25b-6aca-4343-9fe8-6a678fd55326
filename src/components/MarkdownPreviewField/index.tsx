'use client'

import React, { useState, useEffect } from 'react'
import { useFormFields } from '@payloadcms/ui'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Copy, Download, Eye, EyeOff } from 'lucide-react'

export const MarkdownPreviewField: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [markdown, setMarkdown] = useState('')
  const [copySuccess, setCopySuccess] = useState(false)

  // Get the markdown field value
  const markdownField = useFormFields(([fields]) => fields.markdown)

  useEffect(() => {
    if (markdownField?.value) {
      setMarkdown(markdownField.value as string)
    }
  }, [markdownField?.value])

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(markdown)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (err) {
      console.error('Failed to copy markdown:', err)
    }
  }

  const downloadMarkdown = () => {
    const blob = new Blob([markdown], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'tutorial.md'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (!markdown) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Markdown Preview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500">No content available. Add content to the rich text editor above to see the markdown output.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {isVisible ? <Eye className="h-5 w-5" /> : <EyeOff className="h-5 w-5" />}
            Markdown Preview
            <Badge variant="secondary">{markdown.split('\n').length} lines</Badge>
          </CardTitle>
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setIsVisible(!isVisible)}
            >
              {isVisible ? 'Hide' : 'Show'}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={copyToClipboard}
              disabled={!markdown}
            >
              <Copy className="h-4 w-4 mr-1" />
              {copySuccess ? 'Copied!' : 'Copy'}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={downloadMarkdown}
              disabled={!markdown}
            >
              <Download className="h-4 w-4 mr-1" />
              Download
            </Button>
          </div>
        </div>
      </CardHeader>
      {isVisible && (
        <CardContent>
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              This markdown is automatically generated from your rich text content above. 
              It includes all text formatting, blocks, and custom components.
            </div>
            <div className="relative">
              <pre className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg overflow-auto max-h-96 text-sm">
                <code>{markdown}</code>
              </pre>
            </div>
            <div className="text-xs text-gray-500">
              💡 Tip: You can copy this markdown and use it in other markdown editors or documentation systems.
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}
