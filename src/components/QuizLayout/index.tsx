'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Clock, Trophy, CheckCircle, XCircle, RotateCcw } from 'lucide-react'
import { brandingConfig, getDifficultyColor } from '@/config/branding'

interface QuizQuestion {
  question: any // Rich text content
  type: 'multiple-choice' | 'true-false' | 'fill-blank'
  options?: Array<{
    text: string
    isCorrect: boolean
  }>
  correctAnswer?: string
  explanation?: any // Rich text content
  points: number
  codeExample?: string
}

interface Quiz {
  id: string
  title: string
  description?: string
  category: {
    title: string
    slug: string
    color?: string
  }
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  timeLimit: number // in minutes, 0 = no limit
  passingScore: number // percentage
  questions: QuizQuestion[]
  relatedTutorials?: Array<{
    title: string
    slug: string
    excerpt?: string
  }>
}

interface QuizLayoutProps {
  quiz: Quiz
}

interface QuizState {
  currentQuestion: number
  answers: Record<number, string | string[]>
  timeRemaining: number
  isCompleted: boolean
  showResults: boolean
  score: number
}

export const QuizLayout: React.FC<QuizLayoutProps> = ({ quiz }) => {
  const [quizState, setQuizState] = useState<QuizState>({
    currentQuestion: 0,
    answers: {},
    timeRemaining: quiz.timeLimit * 60, // convert to seconds
    isCompleted: false,
    showResults: false,
    score: 0,
  })

  // Timer effect
  useEffect(() => {
    if (quiz.timeLimit > 0 && quizState.timeRemaining > 0 && !quizState.isCompleted) {
      const timer = setInterval(() => {
        setQuizState((prev) => {
          if (prev.timeRemaining <= 1) {
            return { ...prev, timeRemaining: 0, isCompleted: true, showResults: true }
          }
          return { ...prev, timeRemaining: prev.timeRemaining - 1 }
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [quiz.timeLimit, quizState.timeRemaining, quizState.isCompleted])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const handleAnswer = (answer: string) => {
    setQuizState((prev) => ({
      ...prev,
      answers: { ...prev.answers, [prev.currentQuestion]: answer },
    }))
  }

  const nextQuestion = () => {
    if (quizState.currentQuestion < quiz.questions.length - 1) {
      setQuizState((prev) => ({ ...prev, currentQuestion: prev.currentQuestion + 1 }))
    } else {
      completeQuiz()
    }
  }

  const previousQuestion = () => {
    if (quizState.currentQuestion > 0) {
      setQuizState((prev) => ({ ...prev, currentQuestion: prev.currentQuestion - 1 }))
    }
  }

  const completeQuiz = () => {
    const totalPoints = quiz.questions.reduce((sum, q) => sum + q.points, 0)
    let earnedPoints = 0

    quiz.questions.forEach((question, index) => {
      const userAnswer = quizState.answers[index]
      let isCorrect = false

      if (question.type === 'multiple-choice' || question.type === 'true-false') {
        const correctOption = question.options?.find((opt) => opt.isCorrect)
        isCorrect = userAnswer === correctOption?.text
      } else if (question.type === 'fill-blank') {
        isCorrect =
          (typeof userAnswer === 'string' ? userAnswer.toLowerCase().trim() : '') ===
          question.correctAnswer?.toLowerCase().trim()
      }

      if (isCorrect) {
        earnedPoints += question.points
      }
    })

    const score = Math.round((earnedPoints / totalPoints) * 100)
    setQuizState((prev) => ({
      ...prev,
      isCompleted: true,
      showResults: true,
      score,
    }))
  }

  const restartQuiz = () => {
    setQuizState({
      currentQuestion: 0,
      answers: {},
      timeRemaining: quiz.timeLimit * 60,
      isCompleted: false,
      showResults: false,
      score: 0,
    })
  }

  const currentQuestion = quiz.questions[quizState.currentQuestion]
  const userAnswer = quizState.answers[quizState.currentQuestion]

  if (quizState.showResults) {
    return (
      <div className="min-h-screen py-8">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="text-center mb-8">
            <div className="mb-6">
              {quizState.score >= quiz.passingScore ? (
                <CheckCircle className="h-20 w-20 text-green-500 mx-auto mb-4" />
              ) : (
                <XCircle className="h-20 w-20 text-red-500 mx-auto mb-4" />
              )}
            </div>

            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Quiz Completed!
            </h1>

            <div
              className="text-6xl font-bold mb-4"
              style={{ color: getDifficultyColor(quiz.difficulty) }}
            >
              {quizState.score}%
            </div>

            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
              {quizState.score >= quiz.passingScore
                ? `Congratulations! You passed the ${quiz.title} quiz.`
                : `You need ${quiz.passingScore}% to pass. Keep studying and try again!`}
            </p>

            <div className="flex gap-4 justify-center">
              <Button onClick={restartQuiz} variant="outline">
                <RotateCcw className="h-4 w-4 mr-2" />
                Retake Quiz
              </Button>
              <Link href="/quizzes">
                <Button>
                  <Trophy className="h-4 w-4 mr-2" />
                  More Quizzes
                </Button>
              </Link>
            </div>
          </div>

          {/* Related Tutorials */}
          {quiz.relatedTutorials && quiz.relatedTutorials.length > 0 && (
            <section className="mt-12">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                Continue Learning
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quiz.relatedTutorials.map((tutorial, index) => (
                  <Link key={index} href={`/tutorials/${tutorial.slug}`}>
                    <Card className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardContent className="p-4">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                          {tutorial.title}
                        </h3>
                        {tutorial.excerpt && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {tutorial.excerpt}
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="mb-6">
          <Link href="/quizzes">
            <Button variant="ghost" size="sm" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Quizzes
            </Button>
          </Link>

          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{quiz.title}</h1>
              <div className="flex items-center gap-3 mt-2">
                <Badge
                  variant="secondary"
                  style={{
                    backgroundColor: quiz.category.color + '20',
                    color: quiz.category.color,
                  }}
                >
                  {quiz.category.title}
                </Badge>
                <Badge
                  style={{
                    backgroundColor: getDifficultyColor(quiz.difficulty) + '20',
                    color: getDifficultyColor(quiz.difficulty),
                  }}
                >
                  {quiz.difficulty}
                </Badge>
              </div>
            </div>

            {quiz.timeLimit > 0 && (
              <div className="flex items-center gap-2 text-lg font-mono">
                <Clock className="h-5 w-5" />
                <span className={quizState.timeRemaining < 60 ? 'text-red-500' : ''}>
                  {formatTime(quizState.timeRemaining)}
                </span>
              </div>
            )}
          </div>

          {/* Progress */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
              <span>
                Question {quizState.currentQuestion + 1} of {quiz.questions.length}
              </span>
              <span>
                {Math.round(((quizState.currentQuestion + 1) / quiz.questions.length) * 100)}%
                Complete
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-brand-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${((quizState.currentQuestion + 1) / quiz.questions.length) * 100}%`,
                }}
              ></div>
            </div>
          </div>
        </div>

        {/* Question */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-xl">
              {/* Render rich text question here - simplified for now */}
              Question {quizState.currentQuestion + 1}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Code example if present */}
            {currentQuestion?.codeExample && (
              <div className="bg-gray-900 rounded-lg p-4 mb-4 overflow-x-auto">
                <pre className="text-green-400 font-mono text-sm">
                  {currentQuestion?.codeExample}
                </pre>
              </div>
            )}

            {/* Answer options */}
            <div className="space-y-3">
              {currentQuestion?.type === 'multiple-choice' &&
                currentQuestion?.options?.map((option, index) => (
                  <label key={index} className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="answer"
                      value={option.text}
                      checked={userAnswer === option.text}
                      onChange={(e) => handleAnswer(e.target.value)}
                      className="w-4 h-4 text-brand-600"
                    />
                    <span className="text-gray-900 dark:text-white">{option.text}</span>
                  </label>
                ))}

              {currentQuestion?.type === 'true-false' && (
                <div className="space-y-3">
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="answer"
                      value="True"
                      checked={userAnswer === 'True'}
                      onChange={(e) => handleAnswer(e.target.value)}
                      className="w-4 h-4 text-brand-600"
                    />
                    <span className="text-gray-900 dark:text-white">True</span>
                  </label>
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="answer"
                      value="False"
                      checked={userAnswer === 'False'}
                      onChange={(e) => handleAnswer(e.target.value)}
                      className="w-4 h-4 text-brand-600"
                    />
                    <span className="text-gray-900 dark:text-white">False</span>
                  </label>
                </div>
              )}

              {currentQuestion?.type === 'fill-blank' && (
                <input
                  type="text"
                  value={userAnswer || ''}
                  onChange={(e) => handleAnswer(e.target.value)}
                  placeholder="Type your answer here..."
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                />
              )}
            </div>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={previousQuestion}
            disabled={quizState.currentQuestion === 0}
          >
            Previous
          </Button>

          <Button onClick={nextQuestion} disabled={!userAnswer}>
            {quizState.currentQuestion === quiz.questions.length - 1 ? 'Finish Quiz' : 'Next'}
          </Button>
        </div>
      </div>
    </div>
  )
}
