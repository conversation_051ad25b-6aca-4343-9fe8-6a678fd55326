import React from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Code, ExternalLink, BookOpen } from 'lucide-react'

interface Reference {
  id: string
  term: string
  definition: string
  syntax?: string
  category: {
    title: string
    slug: string
    color?: string
  }
  subcategory?: string
  parameters?: Array<{
    name: string
    type?: string
    required: boolean
    description?: string
  }>
  examples?: Array<{
    title: string
    code: string
    output?: string
    explanation?: string
  }>
  browserSupport?: {
    chrome?: string
    firefox?: string
    safari?: string
    edge?: string
  }
  relatedTerms?: Array<{
    term: string
    slug: string
    definition: string
  }>
  relatedTutorials?: Array<{
    title: string
    slug: string
    excerpt?: string
  }>
}

interface ReferenceLayoutProps {
  reference: Reference
}

const BrowserIcon: React.FC<{ browser: string }> = ({ browser }) => {
  const icons = {
    chrome: '🌐',
    firefox: '🦊',
    safari: '🧭',
    edge: '🔷',
  }
  return <span className="text-lg">{icons[browser as keyof typeof icons] || '🌐'}</span>
}

export const ReferenceLayout: React.FC<ReferenceLayoutProps> = ({ reference }) => {
  return (
    <div className="min-h-screen py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Back Navigation */}
        <div className="mb-6">
          <Link href="/references">
            <Button variant="ghost" size="sm" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to References
            </Button>
          </Link>

          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <Link href="/" className="hover:text-brand-600">
              Home
            </Link>
            <span>/</span>
            <Link href="/references" className="hover:text-brand-600">
              References
            </Link>
            <span>/</span>
            <Link href={`/references/${reference.category.slug}`} className="hover:text-brand-600">
              {reference.category.title}
            </Link>
            <span>/</span>
            <span className="text-gray-900 dark:text-gray-100">{reference.term}</span>
          </nav>
        </div>

        {/* Reference Header */}
        <header className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Badge
              variant="secondary"
              style={{
                backgroundColor: reference.category.color + '20',
                color: reference.category.color,
              }}
            >
              {reference.category.title}
            </Badge>
            {reference.subcategory && <Badge variant="outline">{reference.subcategory}</Badge>}
          </div>

          <h1 className="text-4xl font-bold font-mono text-gray-900 dark:text-white mb-4">
            {reference.term}
          </h1>

          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
            {reference.definition}
          </p>
        </header>

        {/* Syntax Section */}
        {reference.syntax && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Syntax</h2>
            <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
              <pre className="text-green-400 font-mono text-sm">{reference.syntax}</pre>
            </div>
          </section>
        )}

        {/* Parameters Section */}
        {reference.parameters && reference.parameters.length > 0 && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Parameters</h2>
            <div className="overflow-x-auto">
              <table className="w-full border border-gray-200 dark:border-gray-700 rounded-lg">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white">
                      Parameter
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white">
                      Type
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white">
                      Required
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-900 dark:text-white">
                      Description
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {reference.parameters.map((param, index) => (
                    <tr key={index} className="bg-white dark:bg-gray-900">
                      <td className="px-4 py-3 font-mono text-sm text-gray-900 dark:text-white">
                        {param.name}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600 dark:text-gray-400">
                        {param.type || 'Any'}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        <Badge variant={param.required ? 'destructive' : 'secondary'}>
                          {param.required ? 'Required' : 'Optional'}
                        </Badge>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600 dark:text-gray-400">
                        {param.description || 'No description available'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </section>
        )}

        {/* Examples Section */}
        {reference.examples && reference.examples.length > 0 && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Examples</h2>
            <div className="space-y-6">
              {reference.examples.map((example, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-lg">{example.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="bg-gray-900 rounded-lg p-4 mb-4 overflow-x-auto">
                      <pre className="text-green-400 font-mono text-sm whitespace-pre-wrap">
                        {example.code}
                      </pre>
                    </div>

                    {example.output && (
                      <div className="mb-4">
                        <h4 className="font-semibold mb-2">Output:</h4>
                        <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3">
                          <pre className="text-sm">{example.output}</pre>
                        </div>
                      </div>
                    )}

                    {example.explanation && (
                      <div>
                        <h4 className="font-semibold mb-2">Explanation:</h4>
                        <p className="text-gray-600 dark:text-gray-400">{example.explanation}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

        {/* Browser Support Section */}
        {reference.browserSupport && (
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Browser Support
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Object.entries(reference.browserSupport).map(
                ([browser, version]) =>
                  version && (
                    <div
                      key={browser}
                      className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                    >
                      <BrowserIcon browser={browser} />
                      <div>
                        <div className="font-medium capitalize">{browser}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">{version}+</div>
                      </div>
                    </div>
                  ),
              )}
            </div>
          </section>
        )}

        {/* Related Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Related Terms */}
          {reference.relatedTerms && reference.relatedTerms.length > 0 && (
            <section>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                Related Terms
              </h2>
              <div className="space-y-3">
                {reference.relatedTerms.map((term, index) => (
                  <Link key={index} href={`/references/${term.slug}`}>
                    <Card className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardContent className="p-4">
                        <h3 className="font-mono font-semibold text-brand-600 mb-1">{term.term}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                          {term.definition}
                        </p>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </section>
          )}

          {/* Related Tutorials */}
          {reference.relatedTutorials && reference.relatedTutorials.length > 0 && (
            <section>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                Related Tutorials
              </h2>
              <div className="space-y-3">
                {reference.relatedTutorials.map((tutorial, index) => (
                  <Link key={index} href={`/tutorials/${tutorial.slug}`}>
                    <Card className="hover:shadow-md transition-shadow cursor-pointer">
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <BookOpen className="h-5 w-5 text-brand-600 mt-0.5 flex-shrink-0" />
                          <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                              {tutorial.title}
                            </h3>
                            {tutorial.excerpt && (
                              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                {tutorial.excerpt}
                              </p>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  )
}
