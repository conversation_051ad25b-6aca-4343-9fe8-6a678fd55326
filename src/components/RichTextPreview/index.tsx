'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@payloadcms/ui'
import RichText from '@/components/RichText'
import { Card } from '@/components/ui/card'
import { cn } from '@/utilities/ui'
import type { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'

interface RichTextPreviewProps {
  /** The rich text data to preview */
  data?: DefaultTypedEditorState
  /** Custom className for the preview container */
  className?: string
  /** Enable/disable prose styling */
  enableProse?: boolean
  /** Enable/disable gutter */
  enableGutter?: boolean
  /** Custom title for the preview */
  title?: string
  /** Position of the preview button */
  buttonPosition?: 'top' | 'bottom'
  /** Size of the preview modal */
  size?: 'small' | 'medium' | 'large' | 'fullscreen'
  /** Custom button text */
  buttonText?: string
  /** Show word count */
  showWordCount?: boolean
  /** Show character count */
  showCharCount?: boolean
}

// Utility function to extract text content from rich text data
const extractTextContent = (data: DefaultTypedEditorState): string => {
  if (!data?.root?.children) return ''

  const extractFromNode = (node: any): string => {
    if (typeof node === 'string') return node
    if (node.text) return node.text
    if (node.children) {
      return node.children.map(extractFromNode).join('')
    }
    return ''
  }

  return data.root.children.map(extractFromNode).join(' ')
}

// Count words in text
const countWords = (text: string): number => {
  return text
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0).length
}

export const RichTextPreview: React.FC<RichTextPreviewProps> = ({
  data,
  className,
  enableProse = true,
  enableGutter = true,
  title = 'Content Preview',
  buttonPosition = 'top',
  size = 'large',
  buttonText = '👁️ Preview',
  showWordCount = true,
  showCharCount = false,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [textContent, setTextContent] = useState('')
  const [wordCount, setWordCount] = useState(0)
  const [charCount, setCharCount] = useState(0)

  // Debug logging
  console.log('RichTextPreview received data:', data)

  useEffect(() => {
    if (data) {
      const text = extractTextContent(data)
      setTextContent(text)
      setWordCount(countWords(text))
      setCharCount(text.length)
    }
  }, [data])

  const sizeClasses = {
    small: 'max-w-2xl',
    medium: 'max-w-4xl',
    large: 'max-w-6xl',
    fullscreen: 'max-w-[95vw] max-h-[95vh]',
  }

  const PreviewButton = () => (
    <Button onClick={() => setIsOpen(true)} size="small" buttonStyle="secondary" disabled={!data}>
      {buttonText}
    </Button>
  )

  const StatsDisplay = () => (
    <div className="flex gap-4 text-sm text-gray-600 dark:text-gray-400">
      {showWordCount && (
        <span className="flex items-center gap-1">
          📝 <strong>{wordCount}</strong> words
        </span>
      )}
      {showCharCount && (
        <span className="flex items-center gap-1">
          🔤 <strong>{charCount}</strong> characters
        </span>
      )}
    </div>
  )

  if (!data) {
    return (
      <div className="flex items-center gap-2 p-2 text-sm text-gray-500 bg-gray-50 dark:bg-gray-800 rounded">
        ⚠️ No content to preview
      </div>
    )
  }

  return (
    <>
      {/* Preview Button and Stats */}
      <div
        className={cn(
          'flex items-center justify-between gap-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800',
          className,
        )}
      >
        <div className="flex items-center gap-4">
          {buttonPosition === 'top' && <PreviewButton />}
          <StatsDisplay />
        </div>
        {buttonPosition === 'bottom' && <PreviewButton />}
      </div>

      {/* Preview Modal */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
          <Card
            className={cn(
              'relative bg-white dark:bg-gray-900 rounded-lg shadow-2xl overflow-hidden',
              sizeClasses[size],
              size === 'fullscreen' ? 'h-[95vh]' : 'max-h-[80vh]',
            )}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
              <div className="flex items-center gap-3">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{title}</h3>
                <StatsDisplay />
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => {
                    const element = document.createElement('div')
                    element.innerHTML = textContent
                    const text = element.textContent || element.innerText || ''
                    navigator.clipboard
                      .writeText(text)
                      .then(() => {
                        alert('✅ Text content copied to clipboard!')
                      })
                      .catch(() => {
                        alert('❌ Failed to copy to clipboard')
                      })
                  }}
                  size="small"
                  buttonStyle="secondary"
                >
                  📋 Copy Text
                </Button>
                <Button onClick={() => setIsOpen(false)} size="small" buttonStyle="secondary">
                  ✕ Close
                </Button>
              </div>
            </div>

            {/* Content */}
            <div className="overflow-auto flex-1 p-6">
              <RichText
                data={data}
                enableProse={enableProse}
                enableGutter={enableGutter}
                className="max-w-none"
              />
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
              <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center gap-4">
                  <span>💡 This is how your content will appear on the frontend</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>Press</span>
                  <kbd className="px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 rounded">Esc</kbd>
                  <span>to close</span>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}
    </>
  )
}

// Hook for keyboard shortcuts
export const usePreviewKeyboard = (onToggle: () => void) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onToggle()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onToggle])
}

// Export as default for easier importing
export default RichTextPreview
