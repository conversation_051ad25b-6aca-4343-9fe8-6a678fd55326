'use client'

import React, { useState, useEffect } from 'react'

// Simple markdown to HTML converter
const convertMarkdownToHTML = (markdown: string): string => {
  return (
    markdown
      // Headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Bold
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      // Inline code
      .replace(
        /`(.*?)`/gim,
        '<code style="background-color: #f3f4f6; padding: 2px 4px; border-radius: 3px; font-family: monospace;">$1</code>',
      )
      // Links
      .replace(
        /\[([^\]]*)\]\(([^\)]*)\)/gim,
        '<a href="$2" style="color: #0ea5e9; text-decoration: underline;">$1</a>',
      )
      // Horizontal rules
      .replace(
        /^---+$/gim,
        '<hr style="border: none; border-top: 1px solid #e5e7eb; margin: 16px 0;" />',
      )
      // Tables (basic support)
      .replace(/\|(.+)\|/gim, (match, content) => {
        const cells = content
          .split('|')
          .map(
            (cell: string) =>
              `<td style="border: 1px solid #e5e7eb; padding: 8px;">${cell.trim()}</td>`,
          )
          .join('')
        return `<tr>${cells}</tr>`
      })
      // Line breaks
      .replace(/\n\n/gim, '</p><p>')
      .replace(/\n/gim, '<br />')
  )
}

// Count words in text
const countWords = (text: string): number => {
  return text
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0).length
}

// Simple test component to verify the field integration works
export const SimplePreviewField: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [content, setContent] = useState('')
  const [wordCount, setWordCount] = useState(0)

  useEffect(() => {
    const extractContent = () => {
      // Try to find the content in the Lexical editor
      const contentEditableElements = document.querySelectorAll('[contenteditable="true"]')

      for (const element of contentEditableElements) {
        const textContent = element.textContent || (element as HTMLElement).innerText || ''
        if (textContent.trim().length > 50) {
          // Only consider substantial content
          setContent(textContent)
          setWordCount(countWords(textContent))
          return
        }
      }

      // Fallback: look for hidden inputs
      const hiddenInputs = document.querySelectorAll('input[type="hidden"]')
      for (const input of hiddenInputs) {
        const inputElement = input as HTMLInputElement
        if (inputElement.name.includes('content') && inputElement.value) {
          try {
            const parsedData = JSON.parse(inputElement.value)
            if (parsedData && parsedData.root && parsedData.root.children) {
              // Extract text from Lexical structure
              const extractText = (node: any): string => {
                if (node.text) return node.text
                if (node.children) {
                  return node.children.map(extractText).join('')
                }
                return ''
              }
              const text = parsedData.root.children.map(extractText).join('\n')
              setContent(text)
              setWordCount(countWords(text))
              return
            }
          } catch (e) {
            // Continue
          }
        }
      }
    }

    // Initial extraction
    extractContent()

    // Set up periodic checking
    const interval = setInterval(extractContent, 1000)

    // Listen for changes
    const handleChange = () => setTimeout(extractContent, 100)
    document.addEventListener('input', handleChange)
    document.addEventListener('change', handleChange)

    return () => {
      clearInterval(interval)
      document.removeEventListener('input', handleChange)
      document.removeEventListener('change', handleChange)
    }
  }, [])

  if (!isOpen) {
    return (
      <div
        style={{
          marginBottom: '16px',
          padding: '12px',
          backgroundColor: '#f0f9ff',
          border: '1px solid #0ea5e9',
          borderRadius: '6px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <div style={{ marginBottom: '8px' }}>
          <h4
            style={{
              margin: '0 0 4px 0',
              fontSize: '14px',
              fontWeight: '600',
              color: '#0369a1',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
            }}
          >
            📖 Content Preview
          </h4>
          <p
            style={{
              margin: '0',
              fontSize: '12px',
              color: '#0284c7',
            }}
          >
            Preview how your content will appear on the frontend
          </p>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <button
            style={{
              padding: '8px 16px',
              backgroundColor: '#0ea5e9',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '12px',
              cursor: 'pointer',
              fontWeight: '500',
            }}
            onClick={() => setIsOpen(true)}
          >
            👁️ Preview Content
          </button>

          {wordCount > 0 && (
            <div style={{ fontSize: '12px', color: '#6b7280' }}>
              📝 <strong>{wordCount}</strong> words
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Preview Button */}
      <div
        style={{
          marginBottom: '16px',
          padding: '12px',
          backgroundColor: '#f0f9ff',
          border: '1px solid #0ea5e9',
          borderRadius: '6px',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <button
            style={{
              padding: '8px 16px',
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              fontSize: '12px',
              cursor: 'pointer',
              fontWeight: '500',
            }}
            onClick={() => setIsOpen(false)}
          >
            ✕ Close Preview
          </button>

          <div style={{ fontSize: '12px', color: '#6b7280' }}>
            📝 <strong>{wordCount}</strong> words
          </div>
        </div>
      </div>

      {/* Preview Modal */}
      <div
        style={{
          position: 'fixed',
          top: '0',
          left: '0',
          right: '0',
          bottom: '0',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '20px',
        }}
        onClick={() => setIsOpen(false)}
      >
        <div
          style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            maxWidth: '800px',
            maxHeight: '80vh',
            width: '100%',
            overflow: 'hidden',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div
            style={{
              padding: '16px 20px',
              borderBottom: '1px solid #e5e7eb',
              backgroundColor: '#f9fafb',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>Content Preview</h3>
            <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
              <span style={{ fontSize: '12px', color: '#6b7280' }}>📝 {wordCount} words</span>
              <button
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer',
                }}
                onClick={() => setIsOpen(false)}
              >
                ✕
              </button>
            </div>
          </div>

          {/* Content */}
          <div
            style={{
              padding: '20px',
              overflow: 'auto',
              maxHeight: 'calc(80vh - 120px)',
              lineHeight: '1.6',
              fontFamily: 'system-ui, -apple-system, sans-serif',
            }}
          >
            {content ? (
              <div
                dangerouslySetInnerHTML={{
                  __html: `<p>${convertMarkdownToHTML(content)}</p>`,
                }}
                style={{
                  fontSize: '16px',
                  color: '#374151',
                }}
              />
            ) : (
              <p style={{ color: '#6b7280', fontStyle: 'italic' }}>
                No content available for preview. Start typing in the editor above!
              </p>
            )}
          </div>

          {/* Footer */}
          <div
            style={{
              padding: '12px 20px',
              borderTop: '1px solid #e5e7eb',
              backgroundColor: '#f9fafb',
              fontSize: '12px',
              color: '#6b7280',
            }}
          >
            💡 This is how your content will appear to readers on the frontend
          </div>
        </div>
      </div>
    </>
  )
}

// Export both named and default
export default SimplePreviewField
