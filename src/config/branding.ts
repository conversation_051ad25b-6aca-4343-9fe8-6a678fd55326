// GuruDevs Visual Branding Configuration
// Based on modern educational platform design principles

export const brandingConfig = {
  // Brand Identity
  brand: {
    name: 'GuruDevs',
    tagline: 'Master Programming, One Step at a Time',
    description:
      'Your trusted partner for innovative development solutions and comprehensive programming education.',
    mission:
      'To make programming education accessible, interactive, and enjoyable for learners worldwide.',
  },

  // Color Palette
  colors: {
    // Primary Brand Colors
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6', // Main brand blue
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },

    // Secondary Colors
    secondary: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
    },

    // Accent Colors for Programming Languages
    languages: {
      html: '#E34F26',
      css: '#1572B6',
      javascript: '#F7DF1E',
      typescript: '#3178C6',
      python: '#3776AB',
      java: '#ED8B00',
      react: '#61DAFB',
      vue: '#4FC08D',
      angular: '#DD0031',
      node: '#339933',
      php: '#777BB4',
      ruby: '#CC342D',
      go: '#00ADD8',
      rust: '#000000',
      swift: '#FA7343',
      kotlin: '#7F52FF',
    },

    // Semantic Colors
    semantic: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
    },

    // Difficulty Levels
    difficulty: {
      beginner: '#10b981',
      intermediate: '#f59e0b',
      advanced: '#ef4444',
    },
  },

  // Typography
  typography: {
    fonts: {
      primary: 'Geist Sans, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      mono: 'Geist Mono, "SF Mono", Monaco, Inconsolata, "Roboto Mono", Consolas, monospace',
      heading: 'Geist Sans, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    },

    scale: {
      xs: '0.75rem', // 12px
      sm: '0.875rem', // 14px
      base: '1rem', // 16px
      lg: '1.125rem', // 18px
      xl: '1.25rem', // 20px
      '2xl': '1.5rem', // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem', // 48px
      '6xl': '3.75rem', // 60px
    },
  },

  // Spacing & Layout
  spacing: {
    container: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
    },

    sections: {
      xs: '2rem',
      sm: '3rem',
      md: '4rem',
      lg: '6rem',
      xl: '8rem',
    },
  },

  // Component Styles
  components: {
    // Buttons
    button: {
      primary: {
        bg: 'bg-blue-600 hover:bg-blue-700',
        text: 'text-white',
        border: 'border-transparent',
        shadow: 'shadow-sm hover:shadow-md',
      },
      secondary: {
        bg: 'bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700',
        text: 'text-gray-900 dark:text-white',
        border: 'border-gray-300 dark:border-gray-600',
        shadow: 'shadow-sm hover:shadow-md',
      },
      success: {
        bg: 'bg-green-600 hover:bg-green-700',
        text: 'text-white',
        border: 'border-transparent',
        shadow: 'shadow-sm hover:shadow-md',
      },
    },

    // Cards
    card: {
      base: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow',
      interactive:
        'cursor-pointer hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200',
    },

    // Code Blocks
    code: {
      background: 'bg-gray-900 dark:bg-gray-950',
      text: 'text-green-400',
      border: 'border border-gray-700',
      radius: 'rounded-lg',
    },
  },

  // Animation & Transitions
  animations: {
    duration: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
    },

    easing: {
      default: 'cubic-bezier(0.4, 0, 0.2, 1)',
      in: 'cubic-bezier(0.4, 0, 1, 1)',
      out: 'cubic-bezier(0, 0, 0.2, 1)',
      inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },

  // Iconography
  icons: {
    size: {
      xs: '1rem',
      sm: '1.25rem',
      md: '1.5rem',
      lg: '2rem',
      xl: '2.5rem',
    },

    style: 'outline', // or 'solid'
    strokeWidth: 1.5,
  },

  // Content Guidelines
  content: {
    maxWidth: {
      prose: '65ch',
      content: '80ch',
      wide: '120ch',
    },

    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75,
    },
  },

  // Responsive Breakpoints
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // Dark Mode
  darkMode: {
    strategy: 'class', // or 'media'
    attribute: 'data-theme',
  },
}

// Helper functions for consistent styling
export const getBrandColor = (color: string, shade: number = 500) => {
  return (
    brandingConfig.colors.primary[shade as keyof typeof brandingConfig.colors.primary] ||
    brandingConfig.colors.primary[500]
  )
}

export const getLanguageColor = (language: string) => {
  return (
    brandingConfig.colors.languages[
      language.toLowerCase() as keyof typeof brandingConfig.colors.languages
    ] || brandingConfig.colors.primary[500]
  )
}

export const getDifficultyColor = (difficulty: 'beginner' | 'intermediate' | 'advanced') => {
  return brandingConfig.colors.difficulty[difficulty]
}

export const getSemanticColor = (type: 'success' | 'warning' | 'error' | 'info') => {
  return brandingConfig.colors.semantic[type]
}
