import type { Payload } from 'payload'

export const seedEducationalContent = async (payload: Payload): Promise<void> => {
  // Create categories
  const htmlCategory = await payload.create({
    collection: 'categories',
    data: {
      title: 'HTML',
      description: 'Learn the structure and markup of web pages',
      type: 'language',
      color: '#E34F26',
      order: 1,
      featured: true,
      slug: 'html',
    },
  })

  const cssCategory = await payload.create({
    collection: 'categories',
    data: {
      title: 'CSS',
      description: 'Style and design beautiful web pages',
      type: 'language',
      color: '#1572B6',
      order: 2,
      featured: true,
      slug: 'css',
    },
  })

  const jsCategory = await payload.create({
    collection: 'categories',
    data: {
      title: 'JavaScript',
      description: 'Add interactivity and dynamic behavior',
      type: 'language',
      color: '#F7DF1E',
      order: 3,
      featured: true,
      slug: 'javascript',
    },
  })

  const pythonCategory = await payload.create({
    collection: 'categories',
    data: {
      title: 'Python',
      description: 'Learn programming with Python',
      type: 'language',
      color: '#3776AB',
      order: 4,
      featured: true,
      slug: 'python',
    },
  })

  // Create HTML tutorials
  const htmlIntroTutorial = await payload.create({
    collection: 'tutorials',
    data: {
      title: 'HTML Introduction',
      excerpt: 'Learn the basics of HTML and how to create your first web page',
      category: htmlCategory.id,
      difficulty: 'beginner',
      estimatedTime: '15 minutes',
      order: 1,
      content: {
        root: {
          type: 'root',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          children: [
            {
              type: 'paragraph',
              version: 1,
              children: [
                {
                  type: 'text',
                  version: 1,
                  text: 'HTML (HyperText Markup Language) is the standard markup language for creating web pages.',
                },
              ],
            },
          ],
        },
      },
      codeExamples: [
        {
          title: 'Basic HTML Structure',
          language: 'html',
          code: `<!DOCTYPE html>
<html>
<head>
    <title>My First Web Page</title>
</head>
<body>
    <h1>Hello, World!</h1>
    <p>This is my first HTML page.</p>
</body>
</html>`,
          tryItYourself: true,
          expectedOutput: 'A web page with a heading "Hello, World!" and a paragraph.',
        },
      ],
      slug: 'html-introduction',
      _status: 'published',
      publishedAt: new Date().toISOString(),
    },
  })

  const htmlElementsTutorial = await payload.create({
    collection: 'tutorials',
    data: {
      title: 'HTML Elements and Tags',
      excerpt: 'Learn about HTML elements, tags, and attributes',
      category: htmlCategory.id,
      difficulty: 'beginner',
      estimatedTime: '20 minutes',
      order: 2,
      content: {
        root: {
          type: 'root',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          children: [
            {
              type: 'paragraph',
              version: 1,
              children: [
                {
                  type: 'text',
                  version: 1,
                  text: 'HTML elements are the building blocks of HTML pages.',
                },
              ],
            },
          ],
        },
      },
      codeExamples: [
        {
          title: 'Common HTML Elements',
          language: 'html',
          code: `<h1>This is a heading</h1>
<p>This is a paragraph</p>
<a href="https://example.com">This is a link</a>
<img src="image.jpg" alt="Description">
<div>This is a div element</div>`,
          tryItYourself: true,
          expectedOutput: 'Various HTML elements displayed on the page.',
        },
      ],
      previousTutorial: htmlIntroTutorial.id,
      slug: 'html-elements-tags',
      _status: 'published',
      publishedAt: new Date().toISOString(),
    },
  })

  // Update the first tutorial to link to the second
  await payload.update({
    collection: 'tutorials',
    id: htmlIntroTutorial.id,
    data: {
      nextTutorial: htmlElementsTutorial.id,
    },
  })

  // Create CSS tutorial
  await payload.create({
    collection: 'tutorials',
    data: {
      title: 'CSS Introduction',
      excerpt: 'Learn the basics of CSS and how to style web pages',
      category: cssCategory.id,
      difficulty: 'beginner',
      estimatedTime: '20 minutes',
      order: 1,
      content: {
        root: {
          type: 'root',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          children: [
            {
              type: 'paragraph',
              version: 1,
              children: [
                {
                  type: 'text',
                  version: 1,
                  text: 'CSS (Cascading Style Sheets) is used to style and layout web pages.',
                },
              ],
            },
          ],
        },
      },
      codeExamples: [
        {
          title: 'Basic CSS Styling',
          language: 'css',
          code: `body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
}

h1 {
    color: blue;
    text-align: center;
}

p {
    color: #333;
    line-height: 1.6;
}`,
          tryItYourself: true,
          expectedOutput: 'Styled text with blue heading and gray paragraph text.',
        },
      ],
      slug: 'css-introduction',
      _status: 'published',
      publishedAt: new Date().toISOString(),
    },
  })

  // Create HTML Chapter
  const htmlChapter1 = await payload.create({
    collection: 'chapters',
    data: {
      title: 'Getting Started with HTML',
      description: 'Learn the fundamentals of HTML and create your first web page',
      category: htmlCategory.id,
      difficulty: 'beginner',
      order: 1,
      estimatedTime: '2 hours',
      icon: 'play-circle',
      overview: {
        root: {
          type: 'root',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          children: [
            {
              type: 'paragraph',
              version: 1,
              children: [
                {
                  type: 'text',
                  version: 1,
                  text: 'In this chapter, you will learn the basics of HTML (HyperText Markup Language), the foundation of all web pages.',
                },
              ],
            },
          ],
        },
      },
      learningObjectives: [
        { objective: 'Understand what HTML is and its role in web development' },
        { objective: 'Learn the basic structure of an HTML document' },
        { objective: 'Create your first HTML page with essential elements' },
        { objective: 'Understand HTML tags, elements, and attributes' },
      ],
      prerequisites: [
        { prerequisite: 'Basic computer skills' },
        { prerequisite: 'Text editor (VS Code recommended)' },
        { prerequisite: 'Web browser (Chrome, Firefox, Safari)' },
      ],
      slug: 'getting-started-html',
      _status: 'published',
    },
  })

  // Update HTML tutorials to belong to chapter
  await payload.update({
    collection: 'tutorials',
    id: htmlIntroTutorial.id,
    data: {
      chapter: htmlChapter1.id,
      lessonNumber: 1,
    },
  })

  await payload.update({
    collection: 'tutorials',
    id: htmlElementsTutorial.id,
    data: {
      chapter: htmlChapter1.id,
      lessonNumber: 2,
    },
  })

  // Create CSS Chapter
  const cssChapter1 = await payload.create({
    collection: 'chapters',
    data: {
      title: 'CSS Fundamentals',
      description: 'Master the basics of CSS styling and layout',
      category: cssCategory.id,
      difficulty: 'beginner',
      order: 1,
      estimatedTime: '3 hours',
      icon: 'palette',
      overview: {
        root: {
          type: 'root',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          children: [
            {
              type: 'paragraph',
              version: 1,
              children: [
                {
                  type: 'text',
                  version: 1,
                  text: 'Learn how to style your HTML pages with CSS. This chapter covers selectors, properties, values, and the box model.',
                },
              ],
            },
          ],
        },
      },
      learningObjectives: [
        { objective: 'Understand CSS syntax and how it works with HTML' },
        { objective: 'Learn about CSS selectors and specificity' },
        { objective: 'Master the CSS box model' },
        { objective: 'Apply colors, fonts, and basic styling' },
      ],
      prerequisites: [
        { prerequisite: 'Basic HTML knowledge' },
        { prerequisite: 'Completed HTML fundamentals chapter' },
      ],
      slug: 'css-fundamentals',
      _status: 'published',
    },
  })

  // Create JavaScript Chapter
  const jsChapter1 = await payload.create({
    collection: 'chapters',
    data: {
      title: 'JavaScript Basics',
      description: 'Introduction to JavaScript programming fundamentals',
      category: jsCategory.id,
      difficulty: 'beginner',
      order: 1,
      estimatedTime: '4 hours',
      icon: 'code',
      overview: {
        root: {
          type: 'root',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          children: [
            {
              type: 'paragraph',
              version: 1,
              children: [
                {
                  type: 'text',
                  version: 1,
                  text: 'Start your JavaScript journey by learning variables, data types, functions, and basic programming concepts.',
                },
              ],
            },
          ],
        },
      },
      learningObjectives: [
        { objective: 'Understand JavaScript syntax and basic concepts' },
        { objective: 'Work with variables and data types' },
        { objective: 'Create and use functions' },
        { objective: 'Handle user interactions and events' },
      ],
      prerequisites: [
        { prerequisite: 'Basic HTML and CSS knowledge' },
        { prerequisite: 'Understanding of programming concepts (helpful but not required)' },
      ],
      slug: 'javascript-basics',
      _status: 'published',
    },
  })

  // Create JavaScript tutorial
  const jsIntroTutorial = await payload.create({
    collection: 'tutorials',
    data: {
      title: 'JavaScript Introduction',
      excerpt: 'Learn the basics of JavaScript programming',
      category: jsCategory.id,
      chapter: jsChapter1.id,
      lessonNumber: 1,
      difficulty: 'beginner',
      estimatedTime: '25 minutes',
      order: 1,
      content: {
        root: {
          type: 'root',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          children: [
            {
              type: 'paragraph',
              version: 1,
              children: [
                {
                  type: 'text',
                  version: 1,
                  text: 'JavaScript is a programming language that enables interactive web pages.',
                },
              ],
            },
          ],
        },
      },
      codeExamples: [
        {
          title: 'Your First JavaScript Code',
          language: 'javascript',
          code: `// Display a message
console.log("Hello, World!");

// Variables
let name = "GuruDevs";
let age = 25;

// Function
function greet(name) {
    return "Hello, " + name + "!";
}

// Call the function
console.log(greet("Student"));`,
          tryItYourself: true,
          expectedOutput: 'Console output: Hello, World! and Hello, Student!',
        },
      ],
      slug: 'javascript-introduction',
      _status: 'published',
      publishedAt: new Date().toISOString(),
    },
  })

  // Link chapters
  await payload.update({
    collection: 'chapters',
    id: htmlChapter1.id,
    data: {
      nextChapter: cssChapter1.id,
    },
  })

  await payload.update({
    collection: 'chapters',
    id: cssChapter1.id,
    data: {
      previousChapter: htmlChapter1.id,
      nextChapter: jsChapter1.id,
    },
  })

  await payload.update({
    collection: 'chapters',
    id: jsChapter1.id,
    data: {
      previousChapter: cssChapter1.id,
    },
  })

  console.log('✅ Educational content with chapters seeded successfully!')
}
